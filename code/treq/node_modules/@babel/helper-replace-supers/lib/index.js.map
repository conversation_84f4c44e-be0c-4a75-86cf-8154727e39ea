{"version": 3, "names": ["_helperEnvironmentVisitor", "require", "_helperMemberExpressionToFunctions", "_helperOptimiseCallExpression", "_core", "assignmentExpression", "booleanLiteral", "callExpression", "cloneNode", "identifier", "memberExpression", "sequenceExpression", "stringLiteral", "thisExpression", "t", "ns", "exports", "environmentVisitor", "default", "skipAllButComputedKey", "getPrototypeOfExpression", "objectRef", "isStatic", "file", "isPrivateMethod", "targetRef", "addHelper", "visitor", "traverse", "visitors", "merge", "Super", "path", "state", "node", "parentPath", "isMemberExpression", "object", "handle", "unshadowSuperBindingVisitor", "Scopable", "refName", "binding", "scope", "getOwnBinding", "name", "rename", "specHandlers", "memoise", "superMember", "count", "computed", "property", "memo", "maybeGenerateMemoised", "memoiser", "set", "prop", "has", "get", "_get", "_getThisRefs", "thisRefs", "proto", "getObjectRef", "needAccessFirst", "this", "isDerivedConstructor", "value", "isInStrictMode", "destructureSet", "buildCodeFrameError", "call", "args", "optimiseCall", "optionalCall", "delete", "template", "expression", "ast", "looseHandlers", "Object", "assign", "getSuperRef", "_getSuper<PERSON>ef", "_getSuperRef2", "ReplaceSupers", "constructor", "opts", "_opts$constantSuper", "methodPath", "isClassMethod", "kind", "superRef", "isObjectMethod", "static", "isStaticBlock", "isPrivate", "isMethod", "constant<PERSON>uper", "isLoose", "replace", "refToPreserve", "handler", "shouldSkip", "parent<PERSON><PERSON>", "memberExpressionToFunctions", "bind", "boundGet"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { File, NodePath, Scope } from \"@babel/core\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type { HandlerState } from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport { traverse, template, types as t } from \"@babel/core\";\nconst {\n  assignmentExpression,\n  booleanLiteral,\n  callExpression,\n  cloneNode,\n  identifier,\n  memberExpression,\n  sequenceExpression,\n  stringLiteral,\n  thisExpression,\n} = t;\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  const ns = require(\"@babel/helper-environment-visitor\");\n  // eslint-disable-next-line no-restricted-globals\n  exports.environmentVisitor = ns.default;\n  // eslint-disable-next-line no-restricted-globals\n  exports.skipAllButComputedKey = ns.skipAllButComputedKey;\n}\n\ntype ThisRef = {\n  needAccessFirst?: boolean;\n  this: t.ThisExpression;\n};\n\n/**\n * Creates an expression which result is the proto of objectRef.\n *\n * @example <caption>isStatic === true</caption>\n *\n *   helpers.getPrototypeOf(CLASS)\n *\n * @example <caption>isStatic === false</caption>\n *\n *   helpers.getPrototypeOf(CLASS.prototype)\n */\nfunction getPrototypeOfExpression(\n  objectRef: t.Identifier,\n  isStatic: boolean,\n  file: File,\n  isPrivateMethod: boolean,\n) {\n  objectRef = cloneNode(objectRef);\n  const targetRef =\n    isStatic || isPrivateMethod\n      ? objectRef\n      : memberExpression(objectRef, identifier(\"prototype\"));\n\n  return callExpression(file.addHelper(\"getPrototypeOf\"), [targetRef]);\n}\n\nconst visitor = traverse.visitors.merge<\n  HandlerState<ReplaceState> & ReplaceState\n>([\n  environmentVisitor,\n  {\n    Super(path, state) {\n      const { node, parentPath } = path;\n      if (!parentPath.isMemberExpression({ object: node })) return;\n      state.handle(parentPath);\n    },\n  },\n]);\n\nconst unshadowSuperBindingVisitor = traverse.visitors.merge<{\n  refName: string;\n}>([\n  environmentVisitor,\n  {\n    Scopable(path, { refName }) {\n      // https://github.com/Zzzen/babel/pull/1#pullrequestreview-564833183\n      const binding = path.scope.getOwnBinding(refName);\n      if (binding && binding.identifier.name === refName) {\n        path.scope.rename(refName);\n      }\n    },\n  },\n]);\n\ntype SharedState = {\n  file: File;\n  scope: Scope;\n  isDerivedConstructor: boolean;\n  isStatic: boolean;\n  isPrivateMethod: boolean;\n  getObjectRef: () => t.Identifier;\n  getSuperRef: () => t.Identifier;\n  // we dont need boundGet here, but memberExpressionToFunctions handler needs it.\n  boundGet: HandlerState[\"get\"];\n};\n\ntype Handler = HandlerState<SharedState> & SharedState;\ntype SuperMember = NodePath<\n  t.MemberExpression & {\n    object: t.Super;\n    property: Exclude<t.MemberExpression[\"property\"], t.PrivateName>;\n  }\n>;\n\ninterface SpecHandler\n  extends Pick<\n    Handler,\n    | \"memoise\"\n    | \"get\"\n    | \"set\"\n    | \"destructureSet\"\n    | \"call\"\n    | \"optionalCall\"\n    | \"delete\"\n  > {\n  _get(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    thisRefs: ThisRef,\n  ): t.CallExpression;\n  _getThisRefs(): ThisRef;\n  prop(this: Handler & SpecHandler, superMember: SuperMember): t.Expression;\n}\n\nconst specHandlers: SpecHandler = {\n  memoise(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    count: number,\n  ) {\n    const { scope, node } = superMember;\n    const { computed, property } = node;\n    if (!computed) {\n      return;\n    }\n\n    const memo = scope.maybeGenerateMemoised(property);\n    if (!memo) {\n      return;\n    }\n\n    this.memoiser.set(property, memo, count);\n  },\n\n  prop(this: Handler & SpecHandler, superMember: SuperMember) {\n    const { computed, property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    if (computed) {\n      return cloneNode(property);\n    }\n\n    return stringLiteral((property as t.Identifier).name);\n  },\n\n  get(this: Handler & SpecHandler, superMember: SuperMember) {\n    return this._get(superMember, this._getThisRefs());\n  },\n\n  _get(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    thisRefs: ThisRef,\n  ) {\n    const proto = getPrototypeOfExpression(\n      this.getObjectRef(),\n      this.isStatic,\n      this.file,\n      this.isPrivateMethod,\n    );\n    return callExpression(this.file.addHelper(\"get\"), [\n      thisRefs.needAccessFirst\n        ? sequenceExpression([thisRefs.this, proto])\n        : proto,\n      this.prop(superMember),\n      thisRefs.this,\n    ]);\n  },\n\n  _getThisRefs(this: Handler & SpecHandler): ThisRef {\n    return {\n      needAccessFirst: this.isDerivedConstructor,\n      this: thisExpression(),\n    };\n  },\n\n  set(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const thisRefs = this._getThisRefs();\n    const proto = getPrototypeOfExpression(\n      this.getObjectRef(),\n      this.isStatic,\n      this.file,\n      this.isPrivateMethod,\n    );\n    return callExpression(this.file.addHelper(\"set\"), [\n      thisRefs.needAccessFirst\n        ? sequenceExpression([thisRefs.this, proto])\n        : proto,\n      this.prop(superMember),\n      value,\n      thisRefs.this,\n      booleanLiteral(superMember.isInStrictMode()),\n    ]);\n  },\n\n  destructureSet(this: Handler & SpecHandler, superMember: SuperMember) {\n    throw superMember.buildCodeFrameError(\n      `Destructuring to a super field is not supported yet.`,\n    );\n  },\n\n  call(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    const thisRefs = this._getThisRefs();\n    return optimiseCall(\n      this._get(superMember, thisRefs),\n      cloneNode(thisRefs.this),\n      args,\n      false,\n    );\n  },\n\n  optionalCall(\n    this: Handler & SpecHandler,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    const thisRefs = this._getThisRefs();\n    return optimiseCall(\n      this._get(superMember, thisRefs),\n      cloneNode(thisRefs.this),\n      args,\n      true,\n    );\n  },\n\n  delete(this: Handler & SpecHandler, superMember: SuperMember) {\n    if (superMember.node.computed) {\n      return sequenceExpression([\n        callExpression(this.file.addHelper(\"toPropertyKey\"), [\n          cloneNode(superMember.node.property),\n        ]),\n        template.expression.ast`\n          function () { throw new ReferenceError(\"'delete super[expr]' is invalid\"); }()\n        `,\n      ]);\n    } else {\n      return template.expression.ast`\n        function () { throw new ReferenceError(\"'delete super.prop' is invalid\"); }()\n      `;\n    }\n  },\n};\n\nconst looseHandlers = {\n  ...specHandlers,\n\n  prop(this: Handler & typeof specHandlers, superMember: SuperMember) {\n    const { property } = superMember.node;\n    if (this.memoiser.has(property)) {\n      return cloneNode(this.memoiser.get(property));\n    }\n\n    return cloneNode(property);\n  },\n\n  get(this: Handler & typeof specHandlers, superMember: SuperMember) {\n    const { isStatic, getSuperRef } = this;\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    let object;\n    if (isStatic) {\n      object =\n        getSuperRef() ??\n        memberExpression(identifier(\"Function\"), identifier(\"prototype\"));\n    } else {\n      object = memberExpression(\n        getSuperRef() ?? identifier(\"Object\"),\n        identifier(\"prototype\"),\n      );\n    }\n\n    return memberExpression(object, prop, computed);\n  },\n\n  set(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    value: t.Expression,\n  ) {\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    return assignmentExpression(\n      \"=\",\n      memberExpression(thisExpression(), prop, computed),\n      value,\n    );\n  },\n\n  destructureSet(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n  ) {\n    const { computed } = superMember.node;\n    const prop = this.prop(superMember);\n\n    return memberExpression(thisExpression(), prop, computed);\n  },\n\n  call(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this.get(superMember), thisExpression(), args, false);\n  },\n\n  optionalCall(\n    this: Handler & typeof specHandlers,\n    superMember: SuperMember,\n    args: t.CallExpression[\"arguments\"],\n  ) {\n    return optimiseCall(this.get(superMember), thisExpression(), args, true);\n  },\n};\n\ntype ReplaceSupersOptionsBase = {\n  methodPath: NodePath<\n    | t.ClassMethod\n    | t.ClassProperty\n    | t.ObjectMethod\n    | t.ClassPrivateMethod\n    | t.ClassPrivateProperty\n    | t.StaticBlock\n  >;\n  constantSuper?: boolean;\n  file: File;\n  // objectRef might have been shadowed in child scopes,\n  // in that case, we need to rename related variables.\n  refToPreserve?: t.Identifier;\n};\n\ntype ReplaceSupersOptions = ReplaceSupersOptionsBase &\n  (\n    | { objectRef?: undefined; getObjectRef: () => t.Node }\n    | { objectRef: t.Node; getObjectRef?: undefined }\n  ) &\n  (\n    | { superRef?: undefined; getSuperRef: () => t.Node }\n    | { superRef: t.Node; getSuperRef?: undefined }\n  );\n\ninterface ReplaceState {\n  file: File;\n  scope: Scope;\n  isDerivedConstructor: boolean;\n  isStatic: boolean;\n  isPrivateMethod: boolean;\n  getObjectRef: ReplaceSupers[\"getObjectRef\"];\n  getSuperRef: ReplaceSupers[\"getSuperRef\"];\n}\n\nexport default class ReplaceSupers {\n  constructor(opts: ReplaceSupersOptions) {\n    const path = opts.methodPath;\n\n    this.methodPath = path;\n    this.isDerivedConstructor =\n      path.isClassMethod({ kind: \"constructor\" }) && !!opts.superRef;\n    this.isStatic =\n      path.isObjectMethod() ||\n      // @ts-expect-error static is not in ClassPrivateMethod\n      path.node.static ||\n      path.isStaticBlock?.();\n    this.isPrivateMethod = path.isPrivate() && path.isMethod();\n\n    this.file = opts.file;\n    this.constantSuper = process.env.BABEL_8_BREAKING\n      ? opts.constantSuper\n      : // Fallback to isLoose for backward compatibility\n        opts.constantSuper ?? (opts as any).isLoose;\n    this.opts = opts;\n  }\n\n  declare file: File;\n  declare isDerivedConstructor: boolean;\n  declare constantSuper: boolean;\n  declare isPrivateMethod: boolean;\n  declare isStatic: boolean;\n  declare methodPath: NodePath;\n  declare opts: ReplaceSupersOptions;\n\n  getObjectRef() {\n    return cloneNode(this.opts.objectRef || this.opts.getObjectRef());\n  }\n\n  getSuperRef() {\n    if (this.opts.superRef) return cloneNode(this.opts.superRef);\n    if (this.opts.getSuperRef) {\n      return cloneNode(this.opts.getSuperRef());\n    }\n  }\n\n  replace() {\n    const { methodPath } = this;\n    // https://github.com/babel/babel/issues/11994\n    if (this.opts.refToPreserve) {\n      methodPath.traverse(unshadowSuperBindingVisitor, {\n        refName: this.opts.refToPreserve.name,\n      });\n    }\n\n    const handler = this.constantSuper ? looseHandlers : specHandlers;\n\n    // todo: this should have been handled by the environmentVisitor,\n    // consider add visitSelf support for the path.traverse\n    // @ts-expect-error: Refine typings in packages/babel-traverse/src/types.ts\n    // shouldSkip is accepted in traverseNode\n    visitor.shouldSkip = (path: NodePath) => {\n      if (path.parentPath === methodPath) {\n        if (path.parentKey === \"decorators\" || path.parentKey === \"key\") {\n          return true;\n        }\n      }\n    };\n\n    memberExpressionToFunctions<ReplaceState>(methodPath, visitor, {\n      file: this.file,\n      scope: this.methodPath.scope,\n      isDerivedConstructor: this.isDerivedConstructor,\n      isStatic: this.isStatic,\n      isPrivateMethod: this.isPrivateMethod,\n      getObjectRef: this.getObjectRef.bind(this),\n      getSuperRef: this.getSuperRef.bind(this),\n      // we dont need boundGet here, but memberExpressionToFunctions handler needs it.\n      boundGet: handler.get,\n      ...handler,\n    });\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,kCAAA,GAAAD,OAAA;AAEA,IAAAE,6BAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,MAAM;EACJI,oBAAoB;EACpBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,UAAU;EACVC,gBAAgB;EAChBC,kBAAkB;EAClBC,aAAa;EACbC;AACF,CAAC,GAAGC,WAAC;AAE4D;EAE/D,MAAMC,EAAE,GAAGd,OAAO,CAAC,mCAAmC,CAAC;EAEvDe,OAAO,CAACC,kBAAkB,GAAGF,EAAE,CAACG,OAAO;EAEvCF,OAAO,CAACG,qBAAqB,GAAGJ,EAAE,CAACI,qBAAqB;AAC1D;AAkBA,SAASC,wBAAwBA,CAC/BC,SAAuB,EACvBC,QAAiB,EACjBC,IAAU,EACVC,eAAwB,EACxB;EACAH,SAAS,GAAGb,SAAS,CAACa,SAAS,CAAC;EAChC,MAAMI,SAAS,GACbH,QAAQ,IAAIE,eAAe,GACvBH,SAAS,GACTX,gBAAgB,CAACW,SAAS,EAAEZ,UAAU,CAAC,WAAW,CAAC,CAAC;EAE1D,OAAOF,cAAc,CAACgB,IAAI,CAACG,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAACD,SAAS,CAAC,CAAC;AACtE;AAEA,MAAME,OAAO,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAErC,CACAb,iCAAkB,EAClB;EACEc,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,MAAM;MAAEC,IAAI;MAAEC;IAAW,CAAC,GAAGH,IAAI;IACjC,IAAI,CAACG,UAAU,CAACC,kBAAkB,CAAC;MAAEC,MAAM,EAAEH;IAAK,CAAC,CAAC,EAAE;IACtDD,KAAK,CAACK,MAAM,CAACH,UAAU,CAAC;EAC1B;AACF,CAAC,CACF,CAAC;AAEF,MAAMI,2BAA2B,GAAGX,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAExD,CACDb,iCAAkB,EAClB;EACEuB,QAAQA,CAACR,IAAI,EAAE;IAAES;EAAQ,CAAC,EAAE;IAE1B,MAAMC,OAAO,GAAGV,IAAI,CAACW,KAAK,CAACC,aAAa,CAACH,OAAO,CAAC;IACjD,IAAIC,OAAO,IAAIA,OAAO,CAACjC,UAAU,CAACoC,IAAI,KAAKJ,OAAO,EAAE;MAClDT,IAAI,CAACW,KAAK,CAACG,MAAM,CAACL,OAAO,CAAC;IAC5B;EACF;AACF,CAAC,CACF,CAAC;AA0CF,MAAMM,YAAyB,GAAG;EAChCC,OAAOA,CAELC,WAAwB,EACxBC,KAAa,EACb;IACA,MAAM;MAAEP,KAAK;MAAET;IAAK,CAAC,GAAGe,WAAW;IACnC,MAAM;MAAEE,QAAQ;MAAEC;IAAS,CAAC,GAAGlB,IAAI;IACnC,IAAI,CAACiB,QAAQ,EAAE;MACb;IACF;IAEA,MAAME,IAAI,GAAGV,KAAK,CAACW,qBAAqB,CAACF,QAAQ,CAAC;IAClD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAACC,GAAG,CAACJ,QAAQ,EAAEC,IAAI,EAAEH,KAAK,CAAC;EAC1C,CAAC;EAEDO,IAAIA,CAA8BR,WAAwB,EAAE;IAC1D,MAAM;MAAEE,QAAQ;MAAEC;IAAS,CAAC,GAAGH,WAAW,CAACf,IAAI;IAC/C,IAAI,IAAI,CAACqB,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAO5C,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAACI,GAAG,CAACP,QAAQ,CAAC,CAAC;IAC/C;IAEA,IAAID,QAAQ,EAAE;MACZ,OAAO3C,SAAS,CAAC4C,QAAQ,CAAC;IAC5B;IAEA,OAAOxC,aAAa,CAAEwC,QAAQ,CAAkBP,IAAI,CAAC;EACvD,CAAC;EAEDc,GAAGA,CAA8BV,WAAwB,EAAE;IACzD,OAAO,IAAI,CAACW,IAAI,CAACX,WAAW,EAAE,IAAI,CAACY,YAAY,CAAC,CAAC,CAAC;EACpD,CAAC;EAEDD,IAAIA,CAEFX,WAAwB,EACxBa,QAAiB,EACjB;IACA,MAAMC,KAAK,GAAG3C,wBAAwB,CACpC,IAAI,CAAC4C,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC1C,QAAQ,EACb,IAAI,CAACC,IAAI,EACT,IAAI,CAACC,eACP,CAAC;IACD,OAAOjB,cAAc,CAAC,IAAI,CAACgB,IAAI,CAACG,SAAS,CAAC,KAAK,CAAC,EAAE,CAChDoC,QAAQ,CAACG,eAAe,GACpBtD,kBAAkB,CAAC,CAACmD,QAAQ,CAACI,IAAI,EAAEH,KAAK,CAAC,CAAC,GAC1CA,KAAK,EACT,IAAI,CAACN,IAAI,CAACR,WAAW,CAAC,EACtBa,QAAQ,CAACI,IAAI,CACd,CAAC;EACJ,CAAC;EAEDL,YAAYA,CAAA,EAAuC;IACjD,OAAO;MACLI,eAAe,EAAE,IAAI,CAACE,oBAAoB;MAC1CD,IAAI,EAAErD,cAAc,CAAC;IACvB,CAAC;EACH,CAAC;EAED2C,GAAGA,CAEDP,WAAwB,EACxBmB,KAAmB,EACnB;IACA,MAAMN,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACpC,MAAME,KAAK,GAAG3C,wBAAwB,CACpC,IAAI,CAAC4C,YAAY,CAAC,CAAC,EACnB,IAAI,CAAC1C,QAAQ,EACb,IAAI,CAACC,IAAI,EACT,IAAI,CAACC,eACP,CAAC;IACD,OAAOjB,cAAc,CAAC,IAAI,CAACgB,IAAI,CAACG,SAAS,CAAC,KAAK,CAAC,EAAE,CAChDoC,QAAQ,CAACG,eAAe,GACpBtD,kBAAkB,CAAC,CAACmD,QAAQ,CAACI,IAAI,EAAEH,KAAK,CAAC,CAAC,GAC1CA,KAAK,EACT,IAAI,CAACN,IAAI,CAACR,WAAW,CAAC,EACtBmB,KAAK,EACLN,QAAQ,CAACI,IAAI,EACb5D,cAAc,CAAC2C,WAAW,CAACoB,cAAc,CAAC,CAAC,CAAC,CAC7C,CAAC;EACJ,CAAC;EAEDC,cAAcA,CAA8BrB,WAAwB,EAAE;IACpE,MAAMA,WAAW,CAACsB,mBAAmB,CAClC,sDACH,CAAC;EACH,CAAC;EAEDC,IAAIA,CAEFvB,WAAwB,EACxBwB,IAAmC,EACnC;IACA,MAAMX,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACpC,OAAO,IAAAa,qCAAY,EACjB,IAAI,CAACd,IAAI,CAACX,WAAW,EAAEa,QAAQ,CAAC,EAChCtD,SAAS,CAACsD,QAAQ,CAACI,IAAI,CAAC,EACxBO,IAAI,EACJ,KACF,CAAC;EACH,CAAC;EAEDE,YAAYA,CAEV1B,WAAwB,EACxBwB,IAAmC,EACnC;IACA,MAAMX,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACpC,OAAO,IAAAa,qCAAY,EACjB,IAAI,CAACd,IAAI,CAACX,WAAW,EAAEa,QAAQ,CAAC,EAChCtD,SAAS,CAACsD,QAAQ,CAACI,IAAI,CAAC,EACxBO,IAAI,EACJ,IACF,CAAC;EACH,CAAC;EAEDG,MAAMA,CAA8B3B,WAAwB,EAAE;IAC5D,IAAIA,WAAW,CAACf,IAAI,CAACiB,QAAQ,EAAE;MAC7B,OAAOxC,kBAAkB,CAAC,CACxBJ,cAAc,CAAC,IAAI,CAACgB,IAAI,CAACG,SAAS,CAAC,eAAe,CAAC,EAAE,CACnDlB,SAAS,CAACyC,WAAW,CAACf,IAAI,CAACkB,QAAQ,CAAC,CACrC,CAAC,EACFyB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAChC;AACA,SAAS,CACF,CAAC;IACJ,CAAC,MAAM;MACL,OAAOF,cAAQ,CAACC,UAAU,CAACC,GAAI;AACrC;AACA,OAAO;IACH;EACF;AACF,CAAC;AAED,MAAMC,aAAa,GAAAC,MAAA,CAAAC,MAAA,KACdnC,YAAY;EAEfU,IAAIA,CAAsCR,WAAwB,EAAE;IAClE,MAAM;MAAEG;IAAS,CAAC,GAAGH,WAAW,CAACf,IAAI;IACrC,IAAI,IAAI,CAACqB,QAAQ,CAACG,GAAG,CAACN,QAAQ,CAAC,EAAE;MAC/B,OAAO5C,SAAS,CAAC,IAAI,CAAC+C,QAAQ,CAACI,GAAG,CAACP,QAAQ,CAAC,CAAC;IAC/C;IAEA,OAAO5C,SAAS,CAAC4C,QAAQ,CAAC;EAC5B,CAAC;EAEDO,GAAGA,CAAsCV,WAAwB,EAAE;IACjE,MAAM;MAAE3B,QAAQ;MAAE6D;IAAY,CAAC,GAAG,IAAI;IACtC,MAAM;MAAEhC;IAAS,CAAC,GAAGF,WAAW,CAACf,IAAI;IACrC,MAAMuB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACR,WAAW,CAAC;IAEnC,IAAIZ,MAAM;IACV,IAAIf,QAAQ,EAAE;MAAA,IAAA8D,YAAA;MACZ/C,MAAM,IAAA+C,YAAA,GACJD,WAAW,CAAC,CAAC,YAAAC,YAAA,GACb1E,gBAAgB,CAACD,UAAU,CAAC,UAAU,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAA4E,aAAA;MACLhD,MAAM,GAAG3B,gBAAgB,EAAA2E,aAAA,GACvBF,WAAW,CAAC,CAAC,YAAAE,aAAA,GAAI5E,UAAU,CAAC,QAAQ,CAAC,EACrCA,UAAU,CAAC,WAAW,CACxB,CAAC;IACH;IAEA,OAAOC,gBAAgB,CAAC2B,MAAM,EAAEoB,IAAI,EAAEN,QAAQ,CAAC;EACjD,CAAC;EAEDK,GAAGA,CAEDP,WAAwB,EACxBmB,KAAmB,EACnB;IACA,MAAM;MAAEjB;IAAS,CAAC,GAAGF,WAAW,CAACf,IAAI;IACrC,MAAMuB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACR,WAAW,CAAC;IAEnC,OAAO5C,oBAAoB,CACzB,GAAG,EACHK,gBAAgB,CAACG,cAAc,CAAC,CAAC,EAAE4C,IAAI,EAAEN,QAAQ,CAAC,EAClDiB,KACF,CAAC;EACH,CAAC;EAEDE,cAAcA,CAEZrB,WAAwB,EACxB;IACA,MAAM;MAAEE;IAAS,CAAC,GAAGF,WAAW,CAACf,IAAI;IACrC,MAAMuB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACR,WAAW,CAAC;IAEnC,OAAOvC,gBAAgB,CAACG,cAAc,CAAC,CAAC,EAAE4C,IAAI,EAAEN,QAAQ,CAAC;EAC3D,CAAC;EAEDqB,IAAIA,CAEFvB,WAAwB,EACxBwB,IAAmC,EACnC;IACA,OAAO,IAAAC,qCAAY,EAAC,IAAI,CAACf,GAAG,CAACV,WAAW,CAAC,EAAEpC,cAAc,CAAC,CAAC,EAAE4D,IAAI,EAAE,KAAK,CAAC;EAC3E,CAAC;EAEDE,YAAYA,CAEV1B,WAAwB,EACxBwB,IAAmC,EACnC;IACA,OAAO,IAAAC,qCAAY,EAAC,IAAI,CAACf,GAAG,CAACV,WAAW,CAAC,EAAEpC,cAAc,CAAC,CAAC,EAAE4D,IAAI,EAAE,IAAI,CAAC;EAC1E;AAAC,EACF;AAsCc,MAAMa,aAAa,CAAC;EACjCC,WAAWA,CAACC,IAA0B,EAAE;IAAA,IAAAC,mBAAA;IACtC,MAAMzD,IAAI,GAAGwD,IAAI,CAACE,UAAU;IAE5B,IAAI,CAACA,UAAU,GAAG1D,IAAI;IACtB,IAAI,CAACmC,oBAAoB,GACvBnC,IAAI,CAAC2D,aAAa,CAAC;MAAEC,IAAI,EAAE;IAAc,CAAC,CAAC,IAAI,CAAC,CAACJ,IAAI,CAACK,QAAQ;IAChE,IAAI,CAACvE,QAAQ,GACXU,IAAI,CAAC8D,cAAc,CAAC,CAAC,IAErB9D,IAAI,CAACE,IAAI,CAAC6D,MAAM,KAChB/D,IAAI,CAACgE,aAAa,oBAAlBhE,IAAI,CAACgE,aAAa,CAAG,CAAC;IACxB,IAAI,CAACxE,eAAe,GAAGQ,IAAI,CAACiE,SAAS,CAAC,CAAC,IAAIjE,IAAI,CAACkE,QAAQ,CAAC,CAAC;IAE1D,IAAI,CAAC3E,IAAI,GAAGiE,IAAI,CAACjE,IAAI;IACrB,IAAI,CAAC4E,aAAa,IAAAV,mBAAA,GAGdD,IAAI,CAACW,aAAa,YAAAV,mBAAA,GAAKD,IAAI,CAASY,OAAO;IAC/C,IAAI,CAACZ,IAAI,GAAGA,IAAI;EAClB;EAUAxB,YAAYA,CAAA,EAAG;IACb,OAAOxD,SAAS,CAAC,IAAI,CAACgF,IAAI,CAACnE,SAAS,IAAI,IAAI,CAACmE,IAAI,CAACxB,YAAY,CAAC,CAAC,CAAC;EACnE;EAEAmB,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACK,IAAI,CAACK,QAAQ,EAAE,OAAOrF,SAAS,CAAC,IAAI,CAACgF,IAAI,CAACK,QAAQ,CAAC;IAC5D,IAAI,IAAI,CAACL,IAAI,CAACL,WAAW,EAAE;MACzB,OAAO3E,SAAS,CAAC,IAAI,CAACgF,IAAI,CAACL,WAAW,CAAC,CAAC,CAAC;IAC3C;EACF;EAEAkB,OAAOA,CAAA,EAAG;IACR,MAAM;MAAEX;IAAW,CAAC,GAAG,IAAI;IAE3B,IAAI,IAAI,CAACF,IAAI,CAACc,aAAa,EAAE;MAC3BZ,UAAU,CAAC9D,QAAQ,CAACW,2BAA2B,EAAE;QAC/CE,OAAO,EAAE,IAAI,CAAC+C,IAAI,CAACc,aAAa,CAACzD;MACnC,CAAC,CAAC;IACJ;IAEA,MAAM0D,OAAO,GAAG,IAAI,CAACJ,aAAa,GAAGnB,aAAa,GAAGjC,YAAY;IAMjEpB,OAAO,CAAC6E,UAAU,GAAIxE,IAAc,IAAK;MACvC,IAAIA,IAAI,CAACG,UAAU,KAAKuD,UAAU,EAAE;QAClC,IAAI1D,IAAI,CAACyE,SAAS,KAAK,YAAY,IAAIzE,IAAI,CAACyE,SAAS,KAAK,KAAK,EAAE;UAC/D,OAAO,IAAI;QACb;MACF;IACF,CAAC;IAED,IAAAC,0CAA2B,EAAehB,UAAU,EAAE/D,OAAO,EAAAsD,MAAA,CAAAC,MAAA;MAC3D3D,IAAI,EAAE,IAAI,CAACA,IAAI;MACfoB,KAAK,EAAE,IAAI,CAAC+C,UAAU,CAAC/C,KAAK;MAC5BwB,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/C7C,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBE,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCwC,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC2C,IAAI,CAAC,IAAI,CAAC;MAC1CxB,WAAW,EAAE,IAAI,CAACA,WAAW,CAACwB,IAAI,CAAC,IAAI,CAAC;MAExCC,QAAQ,EAAEL,OAAO,CAAC5C;IAAG,GAClB4C,OAAO,CACX,CAAC;EACJ;AACF;AAACvF,OAAA,CAAAE,OAAA,GAAAoE,aAAA", "ignoreList": []}