{"version": 3, "names": ["_setPrototypeOf", "require", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "Object", "create", "constructor", "setPrototypeOf"], "sources": ["../../src/helpers/inheritsLoose.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport setPrototypeOf from \"setPrototypeOf\";\n\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AAEe,SAASC,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC3DD,QAAQ,CAACE,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,UAAU,CAACC,SAAS,CAAC;EACxDF,QAAQ,CAACE,SAAS,CAACG,WAAW,GAAGL,QAAQ;EACzCM,eAAc,CAACN,QAAQ,EAAEC,UAAU,CAAC;AACtC", "ignoreList": []}