{"version": 3, "names": ["_unsupportedIterableToArray", "require", "_createForOfIteratorHelper", "o", "allowArrayLike", "it", "Symbol", "iterator", "Array", "isArray", "unsupportedIterableToArray", "length", "i", "F", "s", "n", "done", "value", "e", "f", "TypeError", "normalCompletion", "didErr", "err", "call", "step", "next", "return"], "sources": ["../../src/helpers/createForOfIteratorHelper.js"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport unsupportedIterableToArray from \"unsupportedIterableToArray\";\n\n// s: start (create the iterator)\n// n: next\n// e: error (called whenever something throws)\n// f: finish (always called at the end)\n\nexport default function _createForOfIteratorHelper(o, allowArrayLike) {\n  var it =\n    (typeof Symbol !== \"undefined\" && o[Symbol.iterator]) || o[\"@@iterator\"];\n\n  if (!it) {\n    // Fallback for engines without symbol support\n    if (\n      Array.isArray(o) ||\n      (it = unsupportedIterableToArray(o)) ||\n      (allowArrayLike && o && typeof o.length === \"number\")\n    ) {\n      if (it) o = it;\n      var i = 0;\n      var F = function () {};\n      return {\n        s: F,\n        n: function () {\n          if (i >= o.length) return { done: true };\n          return { done: false, value: o[i++] };\n        },\n        e: function (e) {\n          throw e;\n        },\n        f: F,\n      };\n    }\n\n    throw new TypeError(\n      \"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\",\n    );\n  }\n\n  var normalCompletion = true,\n    didErr = false,\n    err;\n\n  return {\n    s: function () {\n      it = it.call(o);\n    },\n    n: function () {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function (e) {\n      didErr = true;\n      err = e;\n    },\n    f: function () {\n      try {\n        if (!normalCompletion && it.return != null) it.return();\n      } finally {\n        // eslint-disable-next-line no-unsafe-finally\n        if (didErr) throw err;\n      }\n    },\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,2BAAA,GAAAC,OAAA;AAOe,SAASC,0BAA0BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EACpE,IAAIC,EAAE,GACH,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAKJ,CAAC,CAAC,YAAY,CAAC;EAE1E,IAAI,CAACE,EAAE,EAAE;IAEP,IACEG,KAAK,CAACC,OAAO,CAACN,CAAC,CAAC,KACfE,EAAE,GAAGK,2BAA0B,CAACP,CAAC,CAAC,CAAC,IACnCC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACQ,MAAM,KAAK,QAAS,EACrD;MACA,IAAIN,EAAE,EAAEF,CAAC,GAAGE,EAAE;MACd,IAAIO,CAAC,GAAG,CAAC;MACT,IAAIC,CAAC,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;MACtB,OAAO;QACLC,CAAC,EAAED,CAAC;QACJE,CAAC,EAAE,SAAAA,CAAA,EAAY;UACb,IAAIH,CAAC,IAAIT,CAAC,CAACQ,MAAM,EAAE,OAAO;YAAEK,IAAI,EAAE;UAAK,CAAC;UACxC,OAAO;YAAEA,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAEd,CAAC,CAACS,CAAC,EAAE;UAAE,CAAC;QACvC,CAAC;QACDM,CAAC,EAAE,SAAAA,CAAUA,CAAC,EAAE;UACd,MAAMA,CAAC;QACT,CAAC;QACDC,CAAC,EAAEN;MACL,CAAC;IACH;IAEA,MAAM,IAAIO,SAAS,CACjB,uIACF,CAAC;EACH;EAEA,IAAIC,gBAAgB,GAAG,IAAI;IACzBC,MAAM,GAAG,KAAK;IACdC,GAAG;EAEL,OAAO;IACLT,CAAC,EAAE,SAAAA,CAAA,EAAY;MACbT,EAAE,GAAGA,EAAE,CAACmB,IAAI,CAACrB,CAAC,CAAC;IACjB,CAAC;IACDY,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIU,IAAI,GAAGpB,EAAE,CAACqB,IAAI,CAAC,CAAC;MACpBL,gBAAgB,GAAGI,IAAI,CAACT,IAAI;MAC5B,OAAOS,IAAI;IACb,CAAC;IACDP,CAAC,EAAE,SAAAA,CAAUA,CAAC,EAAE;MACdI,MAAM,GAAG,IAAI;MACbC,GAAG,GAAGL,CAAC;IACT,CAAC;IACDC,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAI;QACF,IAAI,CAACE,gBAAgB,IAAIhB,EAAE,CAACsB,MAAM,IAAI,IAAI,EAAEtB,EAAE,CAACsB,MAAM,CAAC,CAAC;MACzD,CAAC,SAAS;QAER,IAAIL,MAAM,EAAE,MAAMC,GAAG;MACvB;IACF;EACF,CAAC;AACH", "ignoreList": []}