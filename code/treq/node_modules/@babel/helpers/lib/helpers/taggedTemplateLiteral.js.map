{"version": 3, "names": ["_taggedTemplateLiteral", "strings", "raw", "slice", "Object", "freeze", "defineProperties", "value"], "sources": ["../../src/helpers/taggedTemplateLiteral.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _taggedTemplateLiteral(strings, raw) {\n  if (!raw) {\n    raw = strings.slice(0);\n  }\n  return Object.freeze(\n    Object.defineProperties(strings, {\n      raw: { value: Object.freeze(raw) },\n    }),\n  );\n}\n"], "mappings": ";;;;;;AAEe,SAASA,sBAAsBA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC3D,IAAI,CAACA,GAAG,EAAE;IACRA,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC;EACxB;EACA,OAAOC,MAAM,CAACC,MAAM,CAClBD,MAAM,CAACE,gBAAgB,CAACL,OAAO,EAAE;IAC/BC,GAAG,EAAE;MAAEK,KAAK,EAAEH,MAAM,CAACC,MAAM,CAACH,GAAG;IAAE;EACnC,CAAC,CACH,CAAC;AACH", "ignoreList": []}