{"version": 3, "names": ["_OverloadYield", "require", "_asyncGeneratorDelegate", "inner", "iter", "waiting", "pump", "key", "value", "Promise", "resolve", "done", "OverloadYield", "Symbol", "iterator", "next", "throw", "return"], "sources": ["../../src/helpers/asyncGeneratorDelegate.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport Overload<PERSON><PERSON> from \"./OverloadYield.ts\";\n\nexport default function _asyncGeneratorDelegate<T>(inner: Generator<T>) {\n  var iter = {} as Generator<T>,\n    // See the comment in AsyncGenerator to understand what this is.\n    waiting = false;\n\n  function pump(\n    key: \"next\" | \"throw\" | \"return\",\n    value: any,\n  ): IteratorYieldResult<any> {\n    waiting = true;\n    value = new Promise(function (resolve) {\n      resolve(inner[key](value));\n    });\n    return {\n      done: false,\n      value: new OverloadYield(value, /* kind: delegate */ 1),\n    };\n  }\n\n  iter[\n    ((typeof Symbol !== \"undefined\" && Symbol.iterator) ||\n      \"@@iterator\") as typeof Symbol.iterator\n  ] = function () {\n    return this;\n  };\n\n  iter.next = function (value: any) {\n    if (waiting) {\n      waiting = false;\n      return value;\n    }\n    return pump(\"next\", value);\n  };\n\n  if (typeof inner.throw === \"function\") {\n    iter.throw = function (value: any) {\n      if (waiting) {\n        waiting = false;\n        throw value;\n      }\n      return pump(\"throw\", value);\n    };\n  }\n\n  if (typeof inner.return === \"function\") {\n    iter.return = function (value: any) {\n      if (waiting) {\n        waiting = false;\n        return value;\n      }\n      return pump(\"return\", value);\n    };\n  }\n\n  return iter;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAEe,SAASC,uBAAuBA,CAAIC,KAAmB,EAAE;EACtE,IAAIC,IAAI,GAAG,CAAC,CAAiB;IAE3BC,OAAO,GAAG,KAAK;EAEjB,SAASC,IAAIA,CACXC,GAAgC,EAChCC,KAAU,EACgB;IAC1BH,OAAO,GAAG,IAAI;IACdG,KAAK,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;MACrCA,OAAO,CAACP,KAAK,CAACI,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,OAAO;MACLG,IAAI,EAAE,KAAK;MACXH,KAAK,EAAE,IAAII,sBAAa,CAACJ,KAAK,EAAuB,CAAC;IACxD,CAAC;EACH;EAEAJ,IAAI,CACA,OAAOS,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAChD,YAAY,CACf,GAAG,YAAY;IACd,OAAO,IAAI;EACb,CAAC;EAEDV,IAAI,CAACW,IAAI,GAAG,UAAUP,KAAU,EAAE;IAChC,IAAIH,OAAO,EAAE;MACXA,OAAO,GAAG,KAAK;MACf,OAAOG,KAAK;IACd;IACA,OAAOF,IAAI,CAAC,MAAM,EAAEE,KAAK,CAAC;EAC5B,CAAC;EAED,IAAI,OAAOL,KAAK,CAACa,KAAK,KAAK,UAAU,EAAE;IACrCZ,IAAI,CAACY,KAAK,GAAG,UAAUR,KAAU,EAAE;MACjC,IAAIH,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;QACf,MAAMG,KAAK;MACb;MACA,OAAOF,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;IAC7B,CAAC;EACH;EAEA,IAAI,OAAOL,KAAK,CAACc,MAAM,KAAK,UAAU,EAAE;IACtCb,IAAI,CAACa,MAAM,GAAG,UAAUT,KAAU,EAAE;MAClC,IAAIH,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;QACf,OAAOG,KAAK;MACd;MACA,OAAOF,IAAI,CAAC,QAAQ,EAAEE,KAAK,CAAC;IAC9B,CAAC;EACH;EAEA,OAAOJ,IAAI;AACb", "ignoreList": []}