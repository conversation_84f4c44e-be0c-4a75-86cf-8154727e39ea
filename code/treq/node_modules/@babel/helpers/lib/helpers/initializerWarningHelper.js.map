{"version": 3, "names": ["_initializerWarningHelper", "descriptor", "context", "Error"], "sources": ["../../src/helpers/initializerWarningHelper.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\n// eslint-disable-next-line no-unused-vars\nexport default function _initializerWarningHelper(descriptor, context) {\n  throw new Error(\n    \"Decorating class property failed. Please ensure that \" +\n      \"transform-class-properties is enabled and runs after the decorators transform.\",\n  );\n}\n"], "mappings": ";;;;;;AAGe,SAASA,yBAAyBA,CAACC,UAAU,EAAEC,OAAO,EAAE;EACrE,MAAM,IAAIC,KAAK,CACb,uDAAuD,GACrD,gFACJ,CAAC;AACH", "ignoreList": []}