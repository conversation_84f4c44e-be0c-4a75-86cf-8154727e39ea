{"version": 3, "names": ["_applyDecoratedDescriptor", "target", "property", "decorators", "descriptor", "context", "desc", "Object", "keys", "for<PERSON>ach", "key", "enumerable", "configurable", "initializer", "writable", "slice", "reverse", "reduce", "decorator", "value", "call", "undefined", "defineProperty"], "sources": ["../../src/helpers/applyDecoratedDescriptor.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _applyDecoratedDescriptor(\n  target,\n  property,\n  decorators,\n  descriptor,\n  context,\n) {\n  var desc = {};\n  Object.keys(descriptor).forEach(function (key) {\n    desc[key] = descriptor[key];\n  });\n  desc.enumerable = !!desc.enumerable;\n  desc.configurable = !!desc.configurable;\n  if (\"value\" in desc || desc.initializer) {\n    desc.writable = true;\n  }\n\n  desc = decorators\n    .slice()\n    .reverse()\n    .reduce(function (desc, decorator) {\n      return decorator(target, property, desc) || desc;\n    }, desc);\n\n  if (context && desc.initializer !== void 0) {\n    desc.value = desc.initializer ? desc.initializer.call(context) : void 0;\n    desc.initializer = undefined;\n  }\n\n  if (desc.initializer === void 0) {\n    Object.defineProperty(target, property, desc);\n    desc = null;\n  }\n\n  return desc;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,yBAAyBA,CAC/CC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACP;EACA,IAAIC,IAAI,GAAG,CAAC,CAAC;EACbC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7CJ,IAAI,CAACI,GAAG,CAAC,GAAGN,UAAU,CAACM,GAAG,CAAC;EAC7B,CAAC,CAAC;EACFJ,IAAI,CAACK,UAAU,GAAG,CAAC,CAACL,IAAI,CAACK,UAAU;EACnCL,IAAI,CAACM,YAAY,GAAG,CAAC,CAACN,IAAI,CAACM,YAAY;EACvC,IAAI,OAAO,IAAIN,IAAI,IAAIA,IAAI,CAACO,WAAW,EAAE;IACvCP,IAAI,CAACQ,QAAQ,GAAG,IAAI;EACtB;EAEAR,IAAI,GAAGH,UAAU,CACdY,KAAK,CAAC,CAAC,CACPC,OAAO,CAAC,CAAC,CACTC,MAAM,CAAC,UAAUX,IAAI,EAAEY,SAAS,EAAE;IACjC,OAAOA,SAAS,CAACjB,MAAM,EAAEC,QAAQ,EAAEI,IAAI,CAAC,IAAIA,IAAI;EAClD,CAAC,EAAEA,IAAI,CAAC;EAEV,IAAID,OAAO,IAAIC,IAAI,CAACO,WAAW,KAAK,KAAK,CAAC,EAAE;IAC1CP,IAAI,CAACa,KAAK,GAAGb,IAAI,CAACO,WAAW,GAAGP,IAAI,CAACO,WAAW,CAACO,IAAI,CAACf,OAAO,CAAC,GAAG,KAAK,CAAC;IACvEC,IAAI,CAACO,WAAW,GAAGQ,SAAS;EAC9B;EAEA,IAAIf,IAAI,CAACO,WAAW,KAAK,KAAK,CAAC,EAAE;IAC/BN,MAAM,CAACe,cAAc,CAACrB,MAAM,EAAEC,QAAQ,EAAEI,IAAI,CAAC;IAC7CA,IAAI,GAAG,IAAI;EACb;EAEA,OAAOA,IAAI;AACb", "ignoreList": []}