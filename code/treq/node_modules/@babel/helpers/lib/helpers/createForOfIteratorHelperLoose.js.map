{"version": 3, "names": ["_unsupportedIterableToArray", "require", "_createForOfIteratorHelperLoose", "o", "allowArrayLike", "it", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "unsupportedIterableToArray", "length", "i", "done", "value", "TypeError"], "sources": ["../../src/helpers/createForOfIteratorHelperLoose.js"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport unsupportedIterableToArray from \"unsupportedIterableToArray\";\n\nexport default function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it =\n    (typeof Symbol !== \"undefined\" && o[Symbol.iterator]) || o[\"@@iterator\"];\n\n  if (it) return (it = it.call(o)).next.bind(it);\n\n  // Fallback for engines without symbol support\n  if (\n    Array.isArray(o) ||\n    (it = unsupportedIterableToArray(o)) ||\n    (allowArrayLike && o && typeof o.length === \"number\")\n  ) {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return { done: true };\n      return { done: false, value: o[i++] };\n    };\n  }\n\n  throw new TypeError(\n    \"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\",\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,2BAAA,GAAAC,OAAA;AAEe,SAASC,+BAA+BA,CAACC,CAAC,EAAEC,cAAc,EAAE;EACzE,IAAIC,EAAE,GACH,OAAOC,MAAM,KAAK,WAAW,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAKJ,CAAC,CAAC,YAAY,CAAC;EAE1E,IAAIE,EAAE,EAAE,OAAO,CAACA,EAAE,GAAGA,EAAE,CAACG,IAAI,CAACL,CAAC,CAAC,EAAEM,IAAI,CAACC,IAAI,CAACL,EAAE,CAAC;EAG9C,IACEM,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,KACfE,EAAE,GAAGQ,2BAA0B,CAACV,CAAC,CAAC,CAAC,IACnCC,cAAc,IAAID,CAAC,IAAI,OAAOA,CAAC,CAACW,MAAM,KAAK,QAAS,EACrD;IACA,IAAIT,EAAE,EAAEF,CAAC,GAAGE,EAAE;IACd,IAAIU,CAAC,GAAG,CAAC;IACT,OAAO,YAAY;MACjB,IAAIA,CAAC,IAAIZ,CAAC,CAACW,MAAM,EAAE,OAAO;QAAEE,IAAI,EAAE;MAAK,CAAC;MACxC,OAAO;QAAEA,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAEd,CAAC,CAACY,CAAC,EAAE;MAAE,CAAC;IACvC,CAAC;EACH;EAEA,MAAM,IAAIG,SAAS,CACjB,uIACF,CAAC;AACH", "ignoreList": []}