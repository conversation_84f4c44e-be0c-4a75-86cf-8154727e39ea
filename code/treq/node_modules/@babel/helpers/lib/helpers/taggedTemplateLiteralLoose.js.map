{"version": 3, "names": ["_taggedTemplateLiteralLoose", "strings", "raw", "slice"], "sources": ["../../src/helpers/taggedTemplateLiteralLoose.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _taggedTemplateLiteralLoose(strings, raw) {\n  if (!raw) {\n    raw = strings.slice(0);\n  }\n  strings.raw = raw;\n  return strings;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,2BAA2BA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAChE,IAAI,CAACA,GAAG,EAAE;IACRA,GAAG,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC;EACxB;EACAF,OAAO,CAACC,GAAG,GAAGA,GAAG;EACjB,OAAOD,OAAO;AAChB", "ignoreList": []}