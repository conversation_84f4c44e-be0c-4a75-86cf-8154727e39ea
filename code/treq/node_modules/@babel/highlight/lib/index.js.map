{"version": 3, "names": ["_jsTokens", "require", "_helperValidatorIdentifier", "_picocolors", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "colors", "process", "env", "FORCE_COLOR", "createColors", "_colors", "compose", "f", "g", "v", "sometimesKeywords", "Set", "getDefs", "keyword", "cyan", "capitalized", "yellow", "jsxIdentifier", "punctuator", "number", "magenta", "string", "green", "regex", "comment", "gray", "invalid", "white", "bgRed", "bold", "NEWLINE", "BRACKET", "tokenize", "JSX_TAG", "getTokenType", "token", "offset", "text", "type", "isKeyword", "value", "isStrictReservedWord", "test", "slice", "toLowerCase", "match", "jsTokens", "exec", "matchToToken", "index", "highlightTokens", "defs", "highlighted", "colorize", "split", "map", "str", "join", "should<PERSON><PERSON><PERSON>", "options", "isColorSupported", "forceColor", "pcWithForcedColor", "undefined", "getColors", "_pcWithForcedColor", "highlight", "code", "chalk", "chalkWithForcedColor", "exports", "getChalk", "_chalk", "_chalkWithForcedColor", "constructor", "enabled", "level"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { Token as J<PERSON><PERSON>, J<PERSON><PERSON>oken } from \"js-tokens\";\nimport jsTokens from \"js-tokens\";\n\nimport {\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\nimport _colors, { createColors } from \"picocolors\";\nimport type { Colors, Formatter } from \"picocolors/types\";\n// See https://github.com/alexeyraspopov/picocolors/issues/62\nconst colors =\n  typeof process === \"object\" &&\n  (process.env.FORCE_COLOR === \"0\" || process.env.FORCE_COLOR === \"false\")\n    ? createColors(false)\n    : _colors;\n\nconst compose: <T, U, V>(f: (gv: U) => V, g: (v: T) => U) => (v: T) => V =\n  (f, g) => v =>\n    f(g(v));\n\n/**\n * Names that are always allowed as identifiers, but also appear as keywords\n * within certain syntactic productions.\n *\n * https://tc39.es/ecma262/#sec-keywords-and-reserved-words\n *\n * `target` has been omitted since it is very likely going to be a false\n * positive.\n */\nconst sometimesKeywords = new Set([\"as\", \"async\", \"from\", \"get\", \"of\", \"set\"]);\n\ntype InternalTokenType =\n  | \"keyword\"\n  | \"capitalized\"\n  | \"jsxIdentifier\"\n  | \"punctuator\"\n  | \"number\"\n  | \"string\"\n  | \"regex\"\n  | \"comment\"\n  | \"invalid\";\n\ntype Token = {\n  type: InternalTokenType | \"uncolored\";\n  value: string;\n};\n/**\n * Styles for token types.\n */\nfunction getDefs(colors: Colors): Record<InternalTokenType, Formatter> {\n  return {\n    keyword: colors.cyan,\n    capitalized: colors.yellow,\n    jsxIdentifier: colors.yellow,\n    punctuator: colors.yellow,\n    number: colors.magenta,\n    string: colors.green,\n    regex: colors.magenta,\n    comment: colors.gray,\n    invalid: compose(compose(colors.white, colors.bgRed), colors.bold),\n  };\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * RegExp to test for the three types of brackets.\n */\nconst BRACKET = /^[()[\\]{}]$/;\n\nlet tokenize: (\n  text: string,\n) => Generator<{ type: InternalTokenType | \"uncolored\"; value: string }>;\n\nif (process.env.BABEL_8_BREAKING) {\n  /**\n   * Get the type of token, specifying punctuator type.\n   */\n  const getTokenType = function (\n    token: JSToken | JSXToken,\n  ): InternalTokenType | \"uncolored\" {\n    if (token.type === \"IdentifierName\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"Punctuator\" && BRACKET.test(token.value)) {\n      return \"uncolored\";\n    }\n\n    if (token.type === \"Invalid\" && token.value === \"@\") {\n      return \"punctuator\";\n    }\n\n    switch (token.type) {\n      case \"NumericLiteral\":\n        return \"number\";\n\n      case \"StringLiteral\":\n      case \"JSXString\":\n      case \"NoSubstitutionTemplate\":\n        return \"string\";\n\n      case \"RegularExpressionLiteral\":\n        return \"regex\";\n\n      case \"Punctuator\":\n      case \"JSXPunctuator\":\n        return \"punctuator\";\n\n      case \"MultiLineComment\":\n      case \"SingleLineComment\":\n        return \"comment\";\n\n      case \"Invalid\":\n      case \"JSXInvalid\":\n        return \"invalid\";\n\n      case \"JSXIdentifier\":\n        return \"jsxIdentifier\";\n\n      default:\n        return \"uncolored\";\n    }\n  };\n\n  /**\n   * Turn a string of JS into an array of objects.\n   */\n  tokenize = function* (text: string): Generator<Token> {\n    for (const token of jsTokens(text, { jsx: true })) {\n      switch (token.type) {\n        case \"TemplateHead\":\n          yield { type: \"string\", value: token.value.slice(0, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateMiddle\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateTail\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1) };\n          break;\n\n        default:\n          yield {\n            type: getTokenType(token),\n            value: token.value,\n          };\n      }\n    }\n  };\n} else {\n  /**\n   * RegExp to test for what seems to be a JSX tag name.\n   */\n  const JSX_TAG = /^[a-z][\\w-]*$/i;\n\n  // The token here is defined in js-tokens@4. However we don't bother\n  // typing it since the whole block will be removed in Babel 8\n  const getTokenType = function (token: any, offset: number, text: string) {\n    if (token.type === \"name\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (\n        JSX_TAG.test(token.value) &&\n        (text[offset - 1] === \"<\" || text.slice(offset - 2, offset) === \"</\")\n      ) {\n        return \"jsxIdentifier\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"punctuator\" && BRACKET.test(token.value)) {\n      return \"bracket\";\n    }\n\n    if (\n      token.type === \"invalid\" &&\n      (token.value === \"@\" || token.value === \"#\")\n    ) {\n      return \"punctuator\";\n    }\n\n    return token.type;\n  };\n\n  tokenize = function* (text: string) {\n    let match;\n    while ((match = (jsTokens as any).default.exec(text))) {\n      const token = (jsTokens as any).matchToToken(match);\n\n      yield {\n        type: getTokenType(token, match.index, text),\n        value: token.value,\n      };\n    }\n  };\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\nfunction highlightTokens(defs: Record<string, Formatter>, text: string) {\n  let highlighted = \"\";\n\n  for (const { type, value } of tokenize(text)) {\n    const colorize = defs[type];\n    if (colorize) {\n      highlighted += value\n        .split(NEWLINE)\n        .map(str => colorize(str))\n        .join(\"\\n\");\n    } else {\n      highlighted += value;\n    }\n  }\n\n  return highlighted;\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\n\ntype Options = {\n  forceColor?: boolean;\n};\n\n/**\n * Whether the code should be highlighted given the passed options.\n */\nexport function shouldHighlight(options: Options): boolean {\n  return colors.isColorSupported || options.forceColor;\n}\n\nlet pcWithForcedColor: Colors = undefined;\nfunction getColors(forceColor: boolean) {\n  if (forceColor) {\n    pcWithForcedColor ??= createColors(true);\n    return pcWithForcedColor;\n  }\n  return colors;\n}\n\n/**\n * Highlight `code`.\n */\nexport default function highlight(code: string, options: Options = {}): string {\n  if (code !== \"\" && shouldHighlight(options)) {\n    const defs = getDefs(getColors(options.forceColor));\n    return highlightTokens(defs, code);\n  } else {\n    return code;\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  let chalk: any, chalkWithForcedColor: any;\n  // eslint-disable-next-line no-restricted-globals\n  exports.getChalk = ({ forceColor }: Options) => {\n    // eslint-disable-next-line no-restricted-globals\n    chalk ??= require(\"chalk\");\n    if (forceColor) {\n      chalkWithForcedColor ??= new chalk.constructor({\n        enabled: true,\n        level: 1,\n      });\n      return chalkWithForcedColor;\n    }\n    return chalk;\n  };\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,0BAAA,GAAAD,OAAA;AAKA,IAAAE,WAAA,GAAAC,uBAAA,CAAAH,OAAA;AAAmD,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGnD,MAAMW,MAAM,GACV,OAAOC,OAAO,KAAK,QAAQ,KAC1BA,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,GAAG,IAAIF,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,OAAO,CAAC,GACpE,IAAAC,wBAAY,EAAC,KAAK,CAAC,GACnBC,mBAAO;AAEb,MAAMC,OAAkE,GACtEA,CAACC,CAAC,EAAEC,CAAC,KAAKC,CAAC,IACTF,CAAC,CAACC,CAAC,CAACC,CAAC,CAAC,CAAC;AAWX,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAoB9E,SAASC,OAAOA,CAACZ,MAAc,EAAwC;EACrE,OAAO;IACLa,OAAO,EAAEb,MAAM,CAACc,IAAI;IACpBC,WAAW,EAAEf,MAAM,CAACgB,MAAM;IAC1BC,aAAa,EAAEjB,MAAM,CAACgB,MAAM;IAC5BE,UAAU,EAAElB,MAAM,CAACgB,MAAM;IACzBG,MAAM,EAAEnB,MAAM,CAACoB,OAAO;IACtBC,MAAM,EAAErB,MAAM,CAACsB,KAAK;IACpBC,KAAK,EAAEvB,MAAM,CAACoB,OAAO;IACrBI,OAAO,EAAExB,MAAM,CAACyB,IAAI;IACpBC,OAAO,EAAEpB,OAAO,CAACA,OAAO,CAACN,MAAM,CAAC2B,KAAK,EAAE3B,MAAM,CAAC4B,KAAK,CAAC,EAAE5B,MAAM,CAAC6B,IAAI;EACnE,CAAC;AACH;AAKA,MAAMC,OAAO,GAAG,yBAAyB;AAKzC,MAAMC,OAAO,GAAG,aAAa;AAE7B,IAAIC,QAEoE;AA6FjE;EAIL,MAAMC,OAAO,GAAG,gBAAgB;EAIhC,MAAMC,YAAY,GAAG,SAAAA,CAAUC,KAAU,EAAEC,MAAc,EAAEC,IAAY,EAAE;IACvE,IAAIF,KAAK,CAACG,IAAI,KAAK,MAAM,EAAE;MACzB,IACE,IAAAC,oCAAS,EAACJ,KAAK,CAACK,KAAK,CAAC,IACtB,IAAAC,+CAAoB,EAACN,KAAK,CAACK,KAAK,EAAE,IAAI,CAAC,IACvC9B,iBAAiB,CAACvB,GAAG,CAACgD,KAAK,CAACK,KAAK,CAAC,EAClC;QACA,OAAO,SAAS;MAClB;MAEA,IACEP,OAAO,CAACS,IAAI,CAACP,KAAK,CAACK,KAAK,CAAC,KACxBH,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IAAIC,IAAI,CAACM,KAAK,CAACP,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,IAAI,CAAC,EACrE;QACA,OAAO,eAAe;MACxB;MAEA,IAAID,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,KAAKL,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,EAAE;QACnD,OAAO,aAAa;MACtB;IACF;IAEA,IAAIT,KAAK,CAACG,IAAI,KAAK,YAAY,IAAIP,OAAO,CAACW,IAAI,CAACP,KAAK,CAACK,KAAK,CAAC,EAAE;MAC5D,OAAO,SAAS;IAClB;IAEA,IACEL,KAAK,CAACG,IAAI,KAAK,SAAS,KACvBH,KAAK,CAACK,KAAK,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,KAAK,GAAG,CAAC,EAC5C;MACA,OAAO,YAAY;IACrB;IAEA,OAAOL,KAAK,CAACG,IAAI;EACnB,CAAC;EAEDN,QAAQ,GAAG,UAAAA,CAAWK,IAAY,EAAE;IAClC,IAAIQ,KAAK;IACT,OAAQA,KAAK,GAAIC,SAAQ,CAAS5D,OAAO,CAAC6D,IAAI,CAACV,IAAI,CAAC,EAAG;MACrD,MAAMF,KAAK,GAAIW,SAAQ,CAASE,YAAY,CAACH,KAAK,CAAC;MAEnD,MAAM;QACJP,IAAI,EAAEJ,YAAY,CAACC,KAAK,EAAEU,KAAK,CAACI,KAAK,EAAEZ,IAAI,CAAC;QAC5CG,KAAK,EAAEL,KAAK,CAACK;MACf,CAAC;IACH;EACF,CAAC;AACH;AAKA,SAASU,eAAeA,CAACC,IAA+B,EAAEd,IAAY,EAAE;EACtE,IAAIe,WAAW,GAAG,EAAE;EAEpB,KAAK,MAAM;IAAEd,IAAI;IAAEE;EAAM,CAAC,IAAIR,QAAQ,CAACK,IAAI,CAAC,EAAE;IAC5C,MAAMgB,QAAQ,GAAGF,IAAI,CAACb,IAAI,CAAC;IAC3B,IAAIe,QAAQ,EAAE;MACZD,WAAW,IAAIZ,KAAK,CACjBc,KAAK,CAACxB,OAAO,CAAC,CACdyB,GAAG,CAACC,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CACzBC,IAAI,CAAC,IAAI,CAAC;IACf,CAAC,MAAM;MACLL,WAAW,IAAIZ,KAAK;IACtB;EACF;EAEA,OAAOY,WAAW;AACpB;AAaO,SAASM,eAAeA,CAACC,OAAgB,EAAW;EACzD,OAAO3D,MAAM,CAAC4D,gBAAgB,IAAID,OAAO,CAACE,UAAU;AACtD;AAEA,IAAIC,iBAAyB,GAAGC,SAAS;AACzC,SAASC,SAASA,CAACH,UAAmB,EAAE;EACtC,IAAIA,UAAU,EAAE;IAAA,IAAAI,kBAAA;IACd,CAAAA,kBAAA,GAAAH,iBAAiB,YAAAG,kBAAA,GAAjBH,iBAAiB,GAAK,IAAA1D,wBAAY,EAAC,IAAI,CAAC;IACxC,OAAO0D,iBAAiB;EAC1B;EACA,OAAO9D,MAAM;AACf;AAKe,SAASkE,SAASA,CAACC,IAAY,EAAER,OAAgB,GAAG,CAAC,CAAC,EAAU;EAC7E,IAAIQ,IAAI,KAAK,EAAE,IAAIT,eAAe,CAACC,OAAO,CAAC,EAAE;IAC3C,MAAMR,IAAI,GAAGvC,OAAO,CAACoD,SAAS,CAACL,OAAO,CAACE,UAAU,CAAC,CAAC;IACnD,OAAOX,eAAe,CAACC,IAAI,EAAEgB,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,OAAOA,IAAI;EACb;AACF;AAEiE;EAC/D,IAAIC,KAAU,EAAEC,oBAAyB;EAEzCC,OAAO,CAACC,QAAQ,GAAG,CAAC;IAAEV;EAAoB,CAAC,KAAK;IAAA,IAAAW,MAAA;IAE9C,CAAAA,MAAA,GAAAJ,KAAK,YAAAI,MAAA,GAALJ,KAAK,GAAK5F,OAAO,CAAC,OAAO,CAAC;IAC1B,IAAIqF,UAAU,EAAE;MAAA,IAAAY,qBAAA;MACd,CAAAA,qBAAA,GAAAJ,oBAAoB,YAAAI,qBAAA,GAApBJ,oBAAoB,GAAK,IAAID,KAAK,CAACM,WAAW,CAAC;QAC7CC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAOP,oBAAoB;IAC7B;IACA,OAAOD,KAAK;EACd,CAAC;AACH", "ignoreList": []}