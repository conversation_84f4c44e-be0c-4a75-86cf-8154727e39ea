/* This file is automatically generated by scripts/generators/tsconfig.js */
{
  "extends": [
    "../../tsconfig.base.json",
    "../../tsconfig.paths.json"
  ],
  "include": [
    "../../packages/babel-preset-env/src/**/*.ts",
    "../../lib/globals.d.ts",
    "../../scripts/repo-utils/*.d.ts"
  ],
  "references": [
    {
      "path": "../../packages/babel-helper-plugin-utils"
    },
    {
      "path": "../../packages/babel-plugin-bugfix-firefox-class-in-computed-class-key"
    },
    {
      "path": "../../packages/babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression"
    },
    {
      "path": "../../packages/babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining"
    },
    {
      "path": "../../packages/babel-plugin-bugfix-v8-static-class-fields-redefine-readonly"
    },
    {
      "path": "../../packages/babel-plugin-syntax-import-assertions"
    },
    {
      "path": "../../packages/babel-plugin-syntax-import-attributes"
    },
    {
      "path": "../../packages/babel-plugin-transform-arrow-functions"
    },
    {
      "path": "../../packages/babel-plugin-transform-async-generator-functions"
    },
    {
      "path": "../../packages/babel-plugin-transform-async-to-generator"
    },
    {
      "path": "../../packages/babel-plugin-transform-block-scoped-functions"
    },
    {
      "path": "../../packages/babel-plugin-transform-block-scoping"
    },
    {
      "path": "../../packages/babel-plugin-transform-class-properties"
    },
    {
      "path": "../../packages/babel-plugin-transform-class-static-block"
    },
    {
      "path": "../../packages/babel-plugin-transform-classes"
    },
    {
      "path": "../../packages/babel-plugin-transform-computed-properties"
    },
    {
      "path": "../../packages/babel-plugin-transform-destructuring"
    },
    {
      "path": "../../packages/babel-plugin-transform-dotall-regex"
    },
    {
      "path": "../../packages/babel-plugin-transform-duplicate-keys"
    },
    {
      "path": "../../packages/babel-plugin-transform-dynamic-import"
    },
    {
      "path": "../../packages/babel-plugin-transform-exponentiation-operator"
    },
    {
      "path": "../../packages/babel-plugin-transform-export-namespace-from"
    },
    {
      "path": "../../packages/babel-plugin-transform-for-of"
    },
    {
      "path": "../../packages/babel-plugin-transform-function-name"
    },
    {
      "path": "../../packages/babel-plugin-transform-json-strings"
    },
    {
      "path": "../../packages/babel-plugin-transform-literals"
    },
    {
      "path": "../../packages/babel-plugin-transform-logical-assignment-operators"
    },
    {
      "path": "../../packages/babel-plugin-transform-member-expression-literals"
    },
    {
      "path": "../../packages/babel-plugin-transform-modules-amd"
    },
    {
      "path": "../../packages/babel-plugin-transform-modules-commonjs"
    },
    {
      "path": "../../packages/babel-plugin-transform-modules-systemjs"
    },
    {
      "path": "../../packages/babel-plugin-transform-modules-umd"
    },
    {
      "path": "../../packages/babel-plugin-transform-named-capturing-groups-regex"
    },
    {
      "path": "../../packages/babel-plugin-transform-new-target"
    },
    {
      "path": "../../packages/babel-plugin-transform-nullish-coalescing-operator"
    },
    {
      "path": "../../packages/babel-plugin-transform-numeric-separator"
    },
    {
      "path": "../../packages/babel-plugin-transform-object-rest-spread"
    },
    {
      "path": "../../packages/babel-plugin-transform-object-super"
    },
    {
      "path": "../../packages/babel-plugin-transform-optional-catch-binding"
    },
    {
      "path": "../../packages/babel-plugin-transform-optional-chaining"
    },
    {
      "path": "../../packages/babel-plugin-transform-parameters"
    },
    {
      "path": "../../packages/babel-plugin-transform-private-methods"
    },
    {
      "path": "../../packages/babel-plugin-transform-private-property-in-object"
    },
    {
      "path": "../../packages/babel-plugin-transform-property-literals"
    },
    {
      "path": "../../packages/babel-plugin-transform-regenerator"
    },
    {
      "path": "../../packages/babel-plugin-transform-reserved-words"
    },
    {
      "path": "../../packages/babel-plugin-transform-shorthand-properties"
    },
    {
      "path": "../../packages/babel-plugin-transform-spread"
    },
    {
      "path": "../../packages/babel-plugin-transform-sticky-regex"
    },
    {
      "path": "../../packages/babel-plugin-transform-template-literals"
    },
    {
      "path": "../../packages/babel-plugin-transform-typeof-symbol"
    },
    {
      "path": "../../packages/babel-plugin-transform-unicode-escapes"
    },
    {
      "path": "../../packages/babel-plugin-transform-unicode-property-regex"
    },
    {
      "path": "../../packages/babel-plugin-transform-unicode-regex"
    },
    {
      "path": "../../packages/babel-plugin-transform-unicode-sets-regex"
    }
  ]
}