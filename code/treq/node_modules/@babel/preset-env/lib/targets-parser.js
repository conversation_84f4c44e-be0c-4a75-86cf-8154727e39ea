"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _helperCompilationTargets.default;
  }
});
Object.defineProperty(exports, "isBrowsersQueryValid", {
  enumerable: true,
  get: function () {
    return _helperCompilationTargets.isBrowsersQueryValid;
  }
});
var _helperCompilationTargets = require("@babel/helper-compilation-targets");

//# sourceMappingURL=targets-parser.js.map
