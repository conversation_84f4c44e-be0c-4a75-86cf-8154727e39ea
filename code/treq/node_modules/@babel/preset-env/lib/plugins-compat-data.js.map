{"version": 3, "names": ["_plugins", "require", "_pluginBugfixes", "_overlappingPlugins", "_availablePlugins", "keys", "Object", "plugins", "exports", "filterAvailable", "originalPlugins", "pluginsBugfixes", "originalPluginsBugfixes", "overlappingPlugins", "originalOverlappingPlugins", "data", "result", "plugin", "hasOwnProperty", "call", "availablePlugins"], "sources": ["../src/plugins-compat-data.ts"], "sourcesContent": ["import originalPlugins from \"@babel/compat-data/plugins\";\nimport originalPluginsBugfixes from \"@babel/compat-data/plugin-bugfixes\";\nimport originalOverlappingPlugins from \"@babel/compat-data/overlapping-plugins\";\nimport availablePlugins from \"./available-plugins.ts\";\n\nconst keys: <O extends object>(o: O) => (keyof O)[] = Object.keys;\n\nexport const plugins = filterAvailable(originalPlugins);\nexport const pluginsBugfixes = filterAvailable(originalPluginsBugfixes);\nexport const overlappingPlugins = filterAvailable(originalOverlappingPlugins);\n\n// @ts-expect-error: we extend this here, since it's a syntax plugin and thus\n// doesn't make sense to store it in a compat-data package.\noverlappingPlugins[\"syntax-import-attributes\"] = [\"syntax-import-assertions\"];\n\nfunction filterAvailable<Data extends { [name: string]: unknown }>(\n  data: Data,\n): { [Name in keyof Data & keyof typeof availablePlugins]: Data[Name] } {\n  const result = {} as any;\n  for (const plugin of keys(data)) {\n    if (Object.hasOwn(availablePlugins, plugin)) {\n      result[plugin] = data[plugin];\n    }\n  }\n  return result;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAH,OAAA;AAEA,MAAMI,IAA6C,GAAGC,MAAM,CAACD,IAAI;AAE1D,MAAME,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAGE,eAAe,CAACC,QAAe,CAAC;AAChD,MAAMC,eAAe,GAAAH,OAAA,CAAAG,eAAA,GAAGF,eAAe,CAACG,eAAuB,CAAC;AAChE,MAAMC,kBAAkB,GAAAL,OAAA,CAAAK,kBAAA,GAAGJ,eAAe,CAACK,mBAA0B,CAAC;AAI7ED,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAE7E,SAASJ,eAAeA,CACtBM,IAAU,EAC4D;EACtE,MAAMC,MAAM,GAAG,CAAC,CAAQ;EACxB,KAAK,MAAMC,MAAM,IAAIZ,IAAI,CAACU,IAAI,CAAC,EAAE;IAC/B,IAAIG,cAAA,CAAAC,IAAA,CAAcC,yBAAgB,EAAEH,MAAM,CAAC,EAAE;MAC3CD,MAAM,CAACC,MAAM,CAAC,GAAGF,IAAI,CAACE,MAAM,CAAC;IAC/B;EACF;EACA,OAAOD,MAAM;AACf", "ignoreList": []}