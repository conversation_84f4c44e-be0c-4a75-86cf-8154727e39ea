{"version": 3, "names": ["_core", "require", "_helperModuleTransforms", "requireNoInterop", "source", "template", "expression", "ast", "requireInterop", "file", "t", "callExpression", "addHelper", "transformDynamicImport", "path", "noInterop", "buildRequire", "replaceWith", "buildDynamicImport", "node", "specifier"], "sources": ["../src/dynamic-import.ts"], "sourcesContent": ["// Heavily inspired by\n// https://github.com/airbnb/babel-plugin-dynamic-import-node/blob/master/src/utils.js\n\nimport type { File, NodePath } from \"@babel/core\";\nimport { types as t, template } from \"@babel/core\";\nimport { buildDynamicImport } from \"@babel/helper-module-transforms\";\n\nconst requireNoInterop = (source: t.Expression) =>\n  template.expression.ast`require(${source})`;\n\nconst requireInterop = (source: t.Expression, file: File) =>\n  t.callExpression(file.addHelper(\"interopRequireWildcard\"), [\n    requireNoInterop(source),\n  ]);\n\nexport function transformDynamicImport(\n  path: NodePath<t.CallExpression | t.ImportExpression>,\n  noInterop: boolean,\n  file: File,\n) {\n  const buildRequire = noInterop ? requireNoInterop : requireInterop;\n\n  path.replaceWith(\n    buildDynamicImport(path.node, true, false, specifier =>\n      buildRequire(specifier, file),\n    ),\n  );\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAEA,MAAME,gBAAgB,GAAIC,MAAoB,IAC5CC,cAAQ,CAACC,UAAU,CAACC,GAAI,WAAUH,MAAO,GAAE;AAE7C,MAAMI,cAAc,GAAGA,CAACJ,MAAoB,EAAEK,IAAU,KACtDC,WAAC,CAACC,cAAc,CAACF,IAAI,CAACG,SAAS,CAAC,wBAAwB,CAAC,EAAE,CACzDT,gBAAgB,CAACC,MAAM,CAAC,CACzB,CAAC;AAEG,SAASS,sBAAsBA,CACpCC,IAAqD,EACrDC,SAAkB,EAClBN,IAAU,EACV;EACA,MAAMO,YAAY,GAAGD,SAAS,GAAGZ,gBAAgB,GAAGK,cAAc;EAElEM,IAAI,CAACG,WAAW,CACd,IAAAC,0CAAkB,EAACJ,IAAI,CAACK,IAAI,EAAE,IAAI,EAAE,KAAK,EAAEC,SAAS,IAClDJ,YAAY,CAACI,SAAS,EAAEX,IAAI,CAC9B,CACF,CAAC;AACH", "ignoreList": []}