{"version": 3, "names": ["_core", "require", "annexB33FunctionsVisitor", "exports", "VariableDeclaration", "path", "isStrict", "node", "kind", "varScope", "scope", "getFunctionParent", "getProgramParent", "traverse", "functionsToVarVisitor", "names", "Object", "keys", "getBindingIdentifiers", "BlockStatement", "t", "isFunction", "parent", "body", "transformStatementList", "get", "SwitchCase", "paths", "outer", "isFunctionDeclaration", "async", "generator", "parentPath", "isVarScope", "name", "id", "currScope", "hasOwnBinding", "maybeTransformBlockScopedFunction", "removeOwnBinding", "varNode", "variableDeclaration", "variableDeclarator", "toExpression", "_blockHoist", "<PERSON><PERSON><PERSON>", "replaceWith", "registerDeclaration", "<PERSON><PERSON>", "binding", "getOwnBinding", "Expression|Declaration", "skip", "isFunctionParent", "isProgram", "find", "_node$directives", "sourceType", "isClass", "isBlockStatement", "directives", "some", "directive", "value"], "sources": ["../src/annex-B_3_3.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@babel/core\";\n\n// Whenever a function declaration in a nested block scope\n// doesn't conflict with a block-scoped binding from an outer\n// scope, we transform it to a variable declaration.\n//\n// This implements the Annex B.3.3 behavior.\n//\n// TODO(Babel 8): Figure out how this should interact with\n// the transform-block-scoped functions plugin (it feels\n// wrong to handle this transform here), and what we want\n// to do with Annex B behavior in general.\n\n// To avoid confusing block-scoped variables transformed to\n// var with original vars, this transformation happens in two\n// different places:\n//   1. For functions that \"conflict\" with var-variables, in\n//      the VariableDeclaration visitor.\n//   2. For functions that don't conflict with any variable,\n//      in the FunctionDeclaration visitor.\n\nexport const annexB33FunctionsVisitor: Visitor = {\n  VariableDeclaration(path) {\n    if (isStrict(path)) return;\n    if (path.node.kind !== \"var\") return;\n\n    const varScope =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    varScope.path.traverse(functionsToVarVisitor, {\n      names: Object.keys(path.getBindingIdentifiers()),\n    });\n  },\n\n  // NOTE: These two visitors target the same nodes as the\n  // block-scoped-functions plugin\n\n  BlockStatement(path) {\n    if (isStrict(path)) return;\n    if (t.isFunction(path.parent, { body: path.node })) return;\n    transformStatementList(path.get(\"body\"));\n  },\n\n  SwitchCase(path) {\n    if (isStrict(path)) return;\n    transformStatementList(path.get(\"consequent\"));\n  },\n};\n\nfunction transformStatementList(paths: NodePath<t.Statement>[]) {\n  outer: for (const path of paths) {\n    if (!path.isFunctionDeclaration()) continue;\n    // Annex B.3.3 only applies to plain functions.\n    if (path.node.async || path.node.generator) return;\n\n    const { scope } = path.parentPath;\n    if (isVarScope(scope)) return;\n\n    const { name } = path.node.id;\n    let currScope = scope;\n    do {\n      if (currScope.parent.hasOwnBinding(name)) continue outer;\n      currScope = currScope.parent;\n    } while (!isVarScope(currScope));\n\n    maybeTransformBlockScopedFunction(path);\n  }\n}\n\nfunction maybeTransformBlockScopedFunction(\n  path: NodePath<t.FunctionDeclaration>,\n) {\n  const {\n    node,\n    parentPath: { scope },\n  } = path;\n\n  const { id } = node;\n  scope.removeOwnBinding(id.name);\n  node.id = null;\n\n  const varNode = t.variableDeclaration(\"var\", [\n    t.variableDeclarator(id, t.toExpression(node)),\n  ]);\n  // @ts-expect-error undocumented property\n  varNode._blockHoist = 2;\n\n  const [varPath] = path.replaceWith(varNode);\n  scope.registerDeclaration(varPath);\n}\n\nconst functionsToVarVisitor: Visitor<{ names: string[] }> = {\n  Scope(path, { names }) {\n    for (const name of names) {\n      const binding = path.scope.getOwnBinding(name);\n      if (binding && binding.kind === \"hoisted\") {\n        maybeTransformBlockScopedFunction(\n          binding.path as NodePath<t.FunctionDeclaration>,\n        );\n      }\n    }\n  },\n  \"Expression|Declaration\"(path) {\n    path.skip();\n  },\n};\n\nexport function isVarScope(scope: Scope) {\n  return scope.path.isFunctionParent() || scope.path.isProgram();\n}\n\nfunction isStrict(path: NodePath) {\n  return !!path.find(({ node }) => {\n    if (t.isProgram(node)) {\n      if (node.sourceType === \"module\") return true;\n    } else if (t.isClass(node)) {\n      return true;\n    } else if (!t.isBlockStatement(node)) {\n      return false;\n    }\n\n    return node.directives?.some(\n      directive => directive.value.value === \"use strict\",\n    );\n  });\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAsBO,MAAMC,wBAAiC,GAAAC,OAAA,CAAAD,wBAAA,GAAG;EAC/CE,mBAAmBA,CAACC,IAAI,EAAE;IACxB,IAAIC,QAAQ,CAACD,IAAI,CAAC,EAAE;IACpB,IAAIA,IAAI,CAACE,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;IAE9B,MAAMC,QAAQ,GACZJ,IAAI,CAACK,KAAK,CAACC,iBAAiB,CAAC,CAAC,IAAIN,IAAI,CAACK,KAAK,CAACE,gBAAgB,CAAC,CAAC;IAEjEH,QAAQ,CAACJ,IAAI,CAACQ,QAAQ,CAACC,qBAAqB,EAAE;MAC5CC,KAAK,EAAEC,MAAM,CAACC,IAAI,CAACZ,IAAI,CAACa,qBAAqB,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC;EAKDC,cAAcA,CAACd,IAAI,EAAE;IACnB,IAAIC,QAAQ,CAACD,IAAI,CAAC,EAAE;IACpB,IAAIe,WAAC,CAACC,UAAU,CAAChB,IAAI,CAACiB,MAAM,EAAE;MAAEC,IAAI,EAAElB,IAAI,CAACE;IAAK,CAAC,CAAC,EAAE;IACpDiB,sBAAsB,CAACnB,IAAI,CAACoB,GAAG,CAAC,MAAM,CAAC,CAAC;EAC1C,CAAC;EAEDC,UAAUA,CAACrB,IAAI,EAAE;IACf,IAAIC,QAAQ,CAACD,IAAI,CAAC,EAAE;IACpBmB,sBAAsB,CAACnB,IAAI,CAACoB,GAAG,CAAC,YAAY,CAAC,CAAC;EAChD;AACF,CAAC;AAED,SAASD,sBAAsBA,CAACG,KAA8B,EAAE;EAC9DC,KAAK,EAAE,KAAK,MAAMvB,IAAI,IAAIsB,KAAK,EAAE;IAC/B,IAAI,CAACtB,IAAI,CAACwB,qBAAqB,CAAC,CAAC,EAAE;IAEnC,IAAIxB,IAAI,CAACE,IAAI,CAACuB,KAAK,IAAIzB,IAAI,CAACE,IAAI,CAACwB,SAAS,EAAE;IAE5C,MAAM;MAAErB;IAAM,CAAC,GAAGL,IAAI,CAAC2B,UAAU;IACjC,IAAIC,UAAU,CAACvB,KAAK,CAAC,EAAE;IAEvB,MAAM;MAAEwB;IAAK,CAAC,GAAG7B,IAAI,CAACE,IAAI,CAAC4B,EAAE;IAC7B,IAAIC,SAAS,GAAG1B,KAAK;IACrB,GAAG;MACD,IAAI0B,SAAS,CAACd,MAAM,CAACe,aAAa,CAACH,IAAI,CAAC,EAAE,SAASN,KAAK;MACxDQ,SAAS,GAAGA,SAAS,CAACd,MAAM;IAC9B,CAAC,QAAQ,CAACW,UAAU,CAACG,SAAS,CAAC;IAE/BE,iCAAiC,CAACjC,IAAI,CAAC;EACzC;AACF;AAEA,SAASiC,iCAAiCA,CACxCjC,IAAqC,EACrC;EACA,MAAM;IACJE,IAAI;IACJyB,UAAU,EAAE;MAAEtB;IAAM;EACtB,CAAC,GAAGL,IAAI;EAER,MAAM;IAAE8B;EAAG,CAAC,GAAG5B,IAAI;EACnBG,KAAK,CAAC6B,gBAAgB,CAACJ,EAAE,CAACD,IAAI,CAAC;EAC/B3B,IAAI,CAAC4B,EAAE,GAAG,IAAI;EAEd,MAAMK,OAAO,GAAGpB,WAAC,CAACqB,mBAAmB,CAAC,KAAK,EAAE,CAC3CrB,WAAC,CAACsB,kBAAkB,CAACP,EAAE,EAAEf,WAAC,CAACuB,YAAY,CAACpC,IAAI,CAAC,CAAC,CAC/C,CAAC;EAEFiC,OAAO,CAACI,WAAW,GAAG,CAAC;EAEvB,MAAM,CAACC,OAAO,CAAC,GAAGxC,IAAI,CAACyC,WAAW,CAACN,OAAO,CAAC;EAC3C9B,KAAK,CAACqC,mBAAmB,CAACF,OAAO,CAAC;AACpC;AAEA,MAAM/B,qBAAmD,GAAG;EAC1DkC,KAAKA,CAAC3C,IAAI,EAAE;IAAEU;EAAM,CAAC,EAAE;IACrB,KAAK,MAAMmB,IAAI,IAAInB,KAAK,EAAE;MACxB,MAAMkC,OAAO,GAAG5C,IAAI,CAACK,KAAK,CAACwC,aAAa,CAAChB,IAAI,CAAC;MAC9C,IAAIe,OAAO,IAAIA,OAAO,CAACzC,IAAI,KAAK,SAAS,EAAE;QACzC8B,iCAAiC,CAC/BW,OAAO,CAAC5C,IACV,CAAC;MACH;IACF;EACF,CAAC;EACD,wBAAwB8C,CAAC9C,IAAI,EAAE;IAC7BA,IAAI,CAAC+C,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAEM,SAASnB,UAAUA,CAACvB,KAAY,EAAE;EACvC,OAAOA,KAAK,CAACL,IAAI,CAACgD,gBAAgB,CAAC,CAAC,IAAI3C,KAAK,CAACL,IAAI,CAACiD,SAAS,CAAC,CAAC;AAChE;AAEA,SAAShD,QAAQA,CAACD,IAAc,EAAE;EAChC,OAAO,CAAC,CAACA,IAAI,CAACkD,IAAI,CAAC,CAAC;IAAEhD;EAAK,CAAC,KAAK;IAAA,IAAAiD,gBAAA;IAC/B,IAAIpC,WAAC,CAACkC,SAAS,CAAC/C,IAAI,CAAC,EAAE;MACrB,IAAIA,IAAI,CAACkD,UAAU,KAAK,QAAQ,EAAE,OAAO,IAAI;IAC/C,CAAC,MAAM,IAAIrC,WAAC,CAACsC,OAAO,CAACnD,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,CAACa,WAAC,CAACuC,gBAAgB,CAACpD,IAAI,CAAC,EAAE;MACpC,OAAO,KAAK;IACd;IAEA,QAAAiD,gBAAA,GAAOjD,IAAI,CAACqD,UAAU,qBAAfJ,gBAAA,CAAiBK,IAAI,CAC1BC,SAAS,IAAIA,SAAS,CAACC,KAAK,CAACA,KAAK,KAAK,YACzC,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}