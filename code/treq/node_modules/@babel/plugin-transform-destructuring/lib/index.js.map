{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { File, Scope, NodePath } from \"@babel/core\";\n\nfunction isPureVoid(node: t.Node) {\n  return (\n    t.isUnaryExpression(node) &&\n    node.operator === \"void\" &&\n    t.isPureish(node.argument)\n  );\n}\n\nexport function unshiftForXStatementBody(\n  statementPath: NodePath<t.ForXStatement>,\n  newStatements: t.Statement[],\n) {\n  statementPath.ensureBlock();\n  const { scope, node } = statementPath;\n  const bodyScopeBindings = statementPath.get(\"body\").scope.bindings;\n  const hasShadowedBlockScopedBindings = Object.keys(bodyScopeBindings).some(\n    name => scope.hasBinding(name),\n  );\n\n  if (hasShadowedBlockScopedBindings) {\n    // handle shadowed variables referenced in computed keys:\n    // var a = 0;for (const { #x: x, [a++]: y } of z) { const a = 1; }\n    node.body = t.blockStatement([...newStatements, node.body]);\n  } else {\n    (node.body as t.BlockStatement).body.unshift(...newStatements);\n  }\n}\n\n/**\n * Test if an ArrayPattern's elements contain any RestElements.\n */\n\nfunction hasArrayRest(pattern: t.ArrayPattern) {\n  return pattern.elements.some(elem => t.isRestElement(elem));\n}\n\n/**\n * Test if an ObjectPattern's properties contain any RestElements.\n */\n\nfunction hasObjectRest(pattern: t.ObjectPattern) {\n  return pattern.properties.some(prop => t.isRestElement(prop));\n}\n\ninterface UnpackableArrayExpression extends t.ArrayExpression {\n  elements: (null | t.Expression)[];\n}\n\nconst STOP_TRAVERSAL = {};\n\ninterface ArrayUnpackVisitorState {\n  deopt: boolean;\n  bindings: Record<string, t.Identifier>;\n}\n\n// NOTE: This visitor is meant to be used via t.traverse\nconst arrayUnpackVisitor = (\n  node: t.Node,\n  ancestors: t.TraversalAncestors,\n  state: ArrayUnpackVisitorState,\n) => {\n  if (!ancestors.length) {\n    // Top-level node: this is the array literal.\n    return;\n  }\n\n  if (\n    t.isIdentifier(node) &&\n    t.isReferenced(node, ancestors[ancestors.length - 1].node) &&\n    state.bindings[node.name]\n  ) {\n    state.deopt = true;\n    throw STOP_TRAVERSAL;\n  }\n};\n\nexport type DestructuringTransformerNode =\n  | t.VariableDeclaration\n  | t.ExpressionStatement\n  | t.ReturnStatement;\n\ninterface DestructuringTransformerOption {\n  blockHoist?: number;\n  operator?: t.AssignmentExpression[\"operator\"];\n  nodes?: DestructuringTransformerNode[];\n  kind?: t.VariableDeclaration[\"kind\"];\n  scope: Scope;\n  arrayLikeIsIterable: boolean;\n  iterableIsArray: boolean;\n  objectRestNoSymbols: boolean;\n  useBuiltIns: boolean;\n  addHelper: File[\"addHelper\"];\n}\nexport class DestructuringTransformer {\n  private blockHoist: number;\n  private operator: t.AssignmentExpression[\"operator\"];\n  arrayRefSet: Set<string>;\n  private nodes: DestructuringTransformerNode[];\n  private scope: Scope;\n  private kind: t.VariableDeclaration[\"kind\"];\n  private iterableIsArray: boolean;\n  private arrayLikeIsIterable: boolean;\n  private objectRestNoSymbols: boolean;\n  private useBuiltIns: boolean;\n  private addHelper: File[\"addHelper\"];\n  constructor(opts: DestructuringTransformerOption) {\n    this.blockHoist = opts.blockHoist;\n    this.operator = opts.operator;\n    this.arrayRefSet = new Set();\n    this.nodes = opts.nodes || [];\n    this.scope = opts.scope;\n    this.kind = opts.kind;\n    this.iterableIsArray = opts.iterableIsArray;\n    this.arrayLikeIsIterable = opts.arrayLikeIsIterable;\n    this.objectRestNoSymbols = opts.objectRestNoSymbols;\n    this.useBuiltIns = opts.useBuiltIns;\n    this.addHelper = opts.addHelper;\n  }\n\n  getExtendsHelper() {\n    return this.useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : this.addHelper(\"extends\");\n  }\n\n  buildVariableAssignment(\n    id: t.AssignmentExpression[\"left\"],\n    init: t.Expression,\n  ) {\n    let op = this.operator;\n    if (t.isMemberExpression(id) || t.isOptionalMemberExpression(id)) op = \"=\";\n\n    let node: t.ExpressionStatement | t.VariableDeclaration;\n\n    if (op) {\n      node = t.expressionStatement(\n        t.assignmentExpression(\n          op,\n          id,\n          t.cloneNode(init) || this.scope.buildUndefinedNode(),\n        ),\n      );\n    } else {\n      let nodeInit: t.Expression;\n\n      if ((this.kind === \"const\" || this.kind === \"using\") && init === null) {\n        nodeInit = this.scope.buildUndefinedNode();\n      } else {\n        nodeInit = t.cloneNode(init);\n      }\n\n      node = t.variableDeclaration(this.kind, [\n        t.variableDeclarator(id as t.LVal, nodeInit),\n      ]);\n    }\n\n    //@ts-expect-error(todo): document block hoist property\n    node._blockHoist = this.blockHoist;\n\n    return node;\n  }\n\n  buildVariableDeclaration(id: t.Identifier, init: t.Expression) {\n    const declar = t.variableDeclaration(\"var\", [\n      t.variableDeclarator(t.cloneNode(id), t.cloneNode(init)),\n    ]);\n    // @ts-expect-error todo(flow->ts): avoid mutations\n    declar._blockHoist = this.blockHoist;\n    return declar;\n  }\n\n  push(id: t.LVal, _init: t.Expression | null) {\n    const init = t.cloneNode(_init);\n    if (t.isObjectPattern(id)) {\n      this.pushObjectPattern(id, init);\n    } else if (t.isArrayPattern(id)) {\n      this.pushArrayPattern(id, init);\n    } else if (t.isAssignmentPattern(id)) {\n      this.pushAssignmentPattern(id, init);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(id, init));\n    }\n  }\n\n  toArray(node: t.Expression, count?: boolean | number) {\n    if (\n      this.iterableIsArray ||\n      (t.isIdentifier(node) && this.arrayRefSet.has(node.name))\n    ) {\n      return node;\n    } else {\n      return this.scope.toArray(node, count, this.arrayLikeIsIterable);\n    }\n  }\n\n  pushAssignmentPattern(\n    { left, right }: t.AssignmentPattern,\n    valueRef: t.Expression | null,\n  ) {\n    // handle array init with void 0. This also happens when\n    // the value was originally a hole.\n    // const [x = 42] = [void 0,];\n    // -> const x = 42;\n    if (isPureVoid(valueRef)) {\n      this.push(left, right);\n      return;\n    }\n\n    // we need to assign the current value of the assignment to avoid evaluating\n    // it more than once\n    const tempId = this.scope.generateUidIdentifierBasedOnNode(valueRef);\n\n    this.nodes.push(this.buildVariableDeclaration(tempId, valueRef));\n\n    const tempConditional = t.conditionalExpression(\n      t.binaryExpression(\n        \"===\",\n        t.cloneNode(tempId),\n        this.scope.buildUndefinedNode(),\n      ),\n      right,\n      t.cloneNode(tempId),\n    );\n\n    if (t.isPattern(left)) {\n      let patternId;\n      let node;\n\n      if (\n        this.kind === \"const\" ||\n        this.kind === \"let\" ||\n        this.kind === \"using\"\n      ) {\n        patternId = this.scope.generateUidIdentifier(tempId.name);\n        node = this.buildVariableDeclaration(patternId, tempConditional);\n      } else {\n        patternId = tempId;\n\n        node = t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(tempId), tempConditional),\n        );\n      }\n\n      this.nodes.push(node);\n      this.push(left, patternId);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(left, tempConditional));\n    }\n  }\n\n  pushObjectRest(\n    pattern: t.ObjectPattern,\n    objRef: t.Expression,\n    spreadProp: t.RestElement,\n    spreadPropIndex: number,\n  ) {\n    const value = buildObjectExcludingKeys(\n      pattern.properties.slice(0, spreadPropIndex) as t.ObjectProperty[],\n      objRef,\n      this.scope,\n      name => this.addHelper(name),\n      this.objectRestNoSymbols,\n      this.useBuiltIns,\n    );\n    this.nodes.push(this.buildVariableAssignment(spreadProp.argument, value));\n  }\n\n  pushObjectProperty(prop: t.ObjectProperty, propRef: t.Expression) {\n    if (t.isLiteral(prop.key)) prop.computed = true;\n\n    const pattern = prop.value as t.LVal;\n    const objRef = t.memberExpression(\n      t.cloneNode(propRef),\n      prop.key,\n      prop.computed,\n    );\n\n    if (t.isPattern(pattern)) {\n      this.push(pattern, objRef);\n    } else {\n      this.nodes.push(this.buildVariableAssignment(pattern, objRef));\n    }\n  }\n\n  pushObjectPattern(pattern: t.ObjectPattern, objRef: t.Expression) {\n    // https://github.com/babel/babel/issues/681\n\n    if (!pattern.properties.length) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(\n            this.addHelper(\"objectDestructuringEmpty\"),\n            isPureVoid(objRef) ? [] : [objRef],\n          ),\n        ),\n      );\n      return;\n    }\n\n    // if we have more than one properties in this pattern and the objectRef is a\n    // member expression then we need to assign it to a temporary variable so it's\n    // only evaluated once\n\n    if (pattern.properties.length > 1 && !this.scope.isStatic(objRef)) {\n      const temp = this.scope.generateUidIdentifierBasedOnNode(objRef);\n      this.nodes.push(this.buildVariableDeclaration(temp, objRef));\n      objRef = temp;\n    }\n\n    // Replace impure computed key expressions if we have a rest parameter\n    if (hasObjectRest(pattern)) {\n      let copiedPattern: t.ObjectPattern;\n      for (let i = 0; i < pattern.properties.length; i++) {\n        const prop = pattern.properties[i];\n        if (t.isRestElement(prop)) {\n          break;\n        }\n        const key = prop.key;\n        if (prop.computed && !this.scope.isPure(key)) {\n          const name = this.scope.generateUidIdentifierBasedOnNode(key);\n          this.nodes.push(\n            //@ts-expect-error PrivateName has been handled by destructuring-private\n            this.buildVariableDeclaration(name, key),\n          );\n          if (!copiedPattern) {\n            copiedPattern = pattern = {\n              ...pattern,\n              properties: pattern.properties.slice(),\n            };\n          }\n          copiedPattern.properties[i] = {\n            ...prop,\n            key: name,\n          };\n        }\n      }\n    }\n\n    for (let i = 0; i < pattern.properties.length; i++) {\n      const prop = pattern.properties[i];\n      if (t.isRestElement(prop)) {\n        this.pushObjectRest(pattern, objRef, prop, i);\n      } else {\n        this.pushObjectProperty(prop, objRef);\n      }\n    }\n  }\n\n  canUnpackArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: t.Expression,\n  ): arr is UnpackableArrayExpression {\n    // not an array so there's no way we can deal with this\n    if (!t.isArrayExpression(arr)) return false;\n\n    // pattern has less elements than the array and doesn't have a rest so some\n    // elements won't be evaluated\n    if (pattern.elements.length > arr.elements.length) return;\n    if (\n      pattern.elements.length < arr.elements.length &&\n      !hasArrayRest(pattern)\n    ) {\n      return false;\n    }\n\n    for (const elem of pattern.elements) {\n      // deopt on holes\n      if (!elem) return false;\n\n      // deopt on member expressions as they may be included in the RHS\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    for (const elem of arr.elements) {\n      // deopt on spread elements\n      if (t.isSpreadElement(elem)) return false;\n\n      // deopt call expressions as they might change values of LHS variables\n      if (t.isCallExpression(elem)) return false;\n\n      // deopt on member expressions as they may be getter/setters and have side-effects\n      if (t.isMemberExpression(elem)) return false;\n    }\n\n    // deopt on reference to left side identifiers\n    const bindings = t.getBindingIdentifiers(pattern);\n    const state: ArrayUnpackVisitorState = { deopt: false, bindings };\n\n    try {\n      t.traverse(arr, arrayUnpackVisitor, state);\n    } catch (e) {\n      if (e !== STOP_TRAVERSAL) throw e;\n    }\n\n    return !state.deopt;\n  }\n\n  pushUnpackedArrayPattern(\n    pattern: t.ArrayPattern,\n    arr: UnpackableArrayExpression,\n  ) {\n    const holeToUndefined = (el: t.Expression) =>\n      el ?? this.scope.buildUndefinedNode();\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n      if (t.isRestElement(elem)) {\n        this.push(\n          elem.argument,\n          t.arrayExpression(arr.elements.slice(i).map(holeToUndefined)),\n        );\n      } else {\n        this.push(elem, holeToUndefined(arr.elements[i]));\n      }\n    }\n  }\n\n  pushArrayPattern(pattern: t.ArrayPattern, arrayRef: t.Expression | null) {\n    if (arrayRef === null) {\n      this.nodes.push(\n        t.expressionStatement(\n          t.callExpression(this.addHelper(\"objectDestructuringEmpty\"), []),\n        ),\n      );\n      return;\n    }\n    if (!pattern.elements) return;\n\n    // optimise basic array destructuring of an array expression\n    //\n    // we can't do this to a pattern of unequal size to it's right hand\n    // array expression as then there will be values that won't be evaluated\n    //\n    // eg: let [a, b] = [1, 2];\n\n    if (this.canUnpackArrayPattern(pattern, arrayRef)) {\n      this.pushUnpackedArrayPattern(pattern, arrayRef);\n      return;\n    }\n\n    // if we have a rest then we need all the elements so don't tell\n    // `scope.toArray` to only get a certain amount\n\n    const count = !hasArrayRest(pattern) && pattern.elements.length;\n\n    // so we need to ensure that the `arrayRef` is an array, `scope.toArray` will\n    // return a locally bound identifier if it's been inferred to be an array,\n    // otherwise it'll be a call to a helper that will ensure it's one\n\n    const toArray = this.toArray(arrayRef, count);\n\n    if (t.isIdentifier(toArray)) {\n      // we've been given an identifier so it must have been inferred to be an\n      // array\n      arrayRef = toArray;\n    } else {\n      arrayRef = this.scope.generateUidIdentifierBasedOnNode(arrayRef);\n      this.arrayRefSet.add(arrayRef.name);\n      this.nodes.push(this.buildVariableDeclaration(arrayRef, toArray));\n    }\n\n    for (let i = 0; i < pattern.elements.length; i++) {\n      const elem = pattern.elements[i];\n\n      // hole\n      if (!elem) continue;\n\n      let elemRef;\n\n      if (t.isRestElement(elem)) {\n        elemRef = this.toArray(arrayRef);\n        elemRef = t.callExpression(\n          t.memberExpression(elemRef, t.identifier(\"slice\")),\n          [t.numericLiteral(i)],\n        );\n\n        // set the element to the rest element argument since we've dealt with it\n        // being a rest already\n        this.push(elem.argument, elemRef);\n      } else {\n        elemRef = t.memberExpression(arrayRef, t.numericLiteral(i), true);\n        this.push(elem, elemRef);\n      }\n    }\n  }\n\n  init(pattern: t.LVal, ref: t.Expression) {\n    // trying to destructure a value that we can't evaluate more than once so we\n    // need to save it to a variable\n\n    if (!t.isArrayExpression(ref) && !t.isMemberExpression(ref)) {\n      const memo = this.scope.maybeGenerateMemoised(ref, true);\n      if (memo) {\n        this.nodes.push(this.buildVariableDeclaration(memo, t.cloneNode(ref)));\n        ref = memo;\n      }\n    }\n\n    this.push(pattern, ref);\n\n    return this.nodes;\n  }\n}\n\ninterface ExcludingKey {\n  key: t.Expression | t.PrivateName;\n  computed: boolean;\n}\n\nexport function buildObjectExcludingKeys<T extends ExcludingKey>(\n  excludedKeys: T[],\n  objRef: t.Expression,\n  scope: Scope,\n  addHelper: File[\"addHelper\"],\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n): t.CallExpression {\n  // get all the keys that appear in this object before the current spread\n\n  const keys = [];\n  let allLiteral = true;\n  let hasTemplateLiteral = false;\n  for (let i = 0; i < excludedKeys.length; i++) {\n    const prop = excludedKeys[i];\n    const key = prop.key;\n    if (t.isIdentifier(key) && !prop.computed) {\n      keys.push(t.stringLiteral(key.name));\n    } else if (t.isTemplateLiteral(key)) {\n      keys.push(t.cloneNode(key));\n      hasTemplateLiteral = true;\n    } else if (t.isLiteral(key)) {\n      // @ts-expect-error todo(flow->ts) NullLiteral\n      keys.push(t.stringLiteral(String(key.value)));\n    } else if (t.isPrivateName(key)) {\n      // private key is not enumerable\n    } else {\n      keys.push(t.cloneNode(key));\n      allLiteral = false;\n    }\n  }\n\n  let value;\n  if (keys.length === 0) {\n    const extendsHelper = useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : addHelper(\"extends\");\n    value = t.callExpression(extendsHelper, [\n      t.objectExpression([]),\n      t.sequenceExpression([\n        t.callExpression(addHelper(\"objectDestructuringEmpty\"), [\n          t.cloneNode(objRef),\n        ]),\n        t.cloneNode(objRef),\n      ]),\n    ]);\n  } else {\n    let keyExpression: t.Expression = t.arrayExpression(keys);\n\n    if (!allLiteral) {\n      keyExpression = t.callExpression(\n        t.memberExpression(keyExpression, t.identifier(\"map\")),\n        [addHelper(\"toPropertyKey\")],\n      );\n    } else if (!hasTemplateLiteral && !t.isProgram(scope.block)) {\n      // Hoist definition of excluded keys, so that it's not created each time.\n      const programScope = scope.getProgramParent();\n      const id = programScope.generateUidIdentifier(\"excluded\");\n\n      programScope.push({\n        id,\n        init: keyExpression,\n        kind: \"const\",\n      });\n\n      keyExpression = t.cloneNode(id);\n    }\n\n    value = t.callExpression(\n      addHelper(`objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`),\n      [t.cloneNode(objRef), keyExpression],\n    );\n  }\n  return value;\n}\n\nexport function convertVariableDeclaration(\n  path: NodePath<t.VariableDeclaration>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope } = path;\n\n  const nodeKind = node.kind;\n  const nodeLoc = node.loc;\n  const nodes = [];\n\n  for (let i = 0; i < node.declarations.length; i++) {\n    const declar = node.declarations[i];\n\n    const patternId = declar.init;\n    const pattern = declar.id;\n\n    const destructuring: DestructuringTransformer =\n      new DestructuringTransformer({\n        // @ts-expect-error(todo): avoid internal properties access\n        blockHoist: node._blockHoist,\n        nodes: nodes,\n        scope: scope,\n        kind: node.kind,\n        iterableIsArray,\n        arrayLikeIsIterable,\n        useBuiltIns,\n        objectRestNoSymbols,\n        addHelper,\n      });\n\n    if (t.isPattern(pattern)) {\n      destructuring.init(pattern, patternId);\n\n      if (+i !== node.declarations.length - 1) {\n        // we aren't the last declarator so let's just make the\n        // last transformed node inherit from us\n        t.inherits(nodes[nodes.length - 1], declar);\n      }\n    } else {\n      nodes.push(\n        t.inherits(\n          destructuring.buildVariableAssignment(pattern, patternId),\n          declar,\n        ),\n      );\n    }\n  }\n\n  let tail: t.VariableDeclaration | null = null;\n  let nodesOut = [];\n  for (const node of nodes) {\n    if (t.isVariableDeclaration(node)) {\n      if (tail !== null) {\n        // Create a single compound declarations\n        tail.declarations.push(...node.declarations);\n        continue;\n      } else {\n        // Make sure the original node kind is used for each compound declaration\n        node.kind = nodeKind;\n        tail = node;\n      }\n    } else {\n      tail = null;\n    }\n    // Propagate the original declaration node's location\n    if (!node.loc) {\n      node.loc = nodeLoc;\n    }\n    nodesOut.push(node);\n  }\n\n  if (\n    nodesOut.length === 2 &&\n    t.isVariableDeclaration(nodesOut[0]) &&\n    t.isExpressionStatement(nodesOut[1]) &&\n    t.isCallExpression(nodesOut[1].expression) &&\n    nodesOut[0].declarations.length === 1\n  ) {\n    // This can only happen when we generate this code:\n    //    var _ref = DESTRUCTURED_VALUE;\n    //     babelHelpers.objectDestructuringEmpty(_ref);\n    // Since pushing those two statements to the for loop .init will require an IIFE,\n    // we can optimize them to\n    //     babelHelpers.objectDestructuringEmpty(DESTRUCTURED_VALUE);\n    const expr = nodesOut[1].expression;\n    expr.arguments = [nodesOut[0].declarations[0].init];\n    nodesOut = [expr];\n  } else {\n    // We must keep nodes all are expressions or statements, so `replaceWithMultiple` can work.\n    if (\n      t.isForStatement(path.parent, { init: node }) &&\n      !nodesOut.some(v => t.isVariableDeclaration(v))\n    ) {\n      for (let i = 0; i < nodesOut.length; i++) {\n        const node: t.Node = nodesOut[i];\n        if (t.isExpressionStatement(node)) {\n          nodesOut[i] = node.expression;\n        }\n      }\n    }\n  }\n\n  if (nodesOut.length === 1) {\n    path.replaceWith(nodesOut[0]);\n  } else {\n    path.replaceWithMultiple(nodesOut);\n  }\n  scope.crawl();\n}\n\nexport function convertAssignmentExpression(\n  path: NodePath<t.AssignmentExpression & { left: t.Pattern }>,\n  addHelper: File[\"addHelper\"],\n  arrayLikeIsIterable: boolean,\n  iterableIsArray: boolean,\n  objectRestNoSymbols: boolean,\n  useBuiltIns: boolean,\n) {\n  const { node, scope, parentPath } = path;\n\n  const nodes: DestructuringTransformerNode[] = [];\n\n  const destructuring = new DestructuringTransformer({\n    operator: node.operator,\n    scope: scope,\n    nodes: nodes,\n    arrayLikeIsIterable,\n    iterableIsArray,\n    objectRestNoSymbols,\n    useBuiltIns,\n    addHelper,\n  });\n\n  let ref: t.Identifier | void;\n  if (\n    (!parentPath.isExpressionStatement() &&\n      !parentPath.isSequenceExpression()) ||\n    path.isCompletionRecord()\n  ) {\n    ref = scope.generateUidIdentifierBasedOnNode(node.right, \"ref\");\n\n    nodes.push(\n      t.variableDeclaration(\"var\", [t.variableDeclarator(ref, node.right)]),\n    );\n\n    if (t.isArrayExpression(node.right)) {\n      destructuring.arrayRefSet.add(ref.name);\n    }\n  }\n\n  destructuring.init(node.left, ref || node.right);\n\n  if (ref) {\n    if (parentPath.isArrowFunctionExpression()) {\n      path.replaceWith(t.blockStatement([]));\n      nodes.push(t.returnStatement(t.cloneNode(ref)));\n    } else {\n      nodes.push(t.expressionStatement(t.cloneNode(ref)));\n    }\n  }\n\n  path.replaceWithMultiple(nodes);\n  scope.crawl();\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, type NodePath } from \"@babel/core\";\nimport {\n  DestructuringTransformer,\n  convertVariableDeclaration,\n  convertAssignmentExpression,\n  unshiftForXStatementBody,\n  type DestructuringTransformerNode,\n} from \"./util.ts\";\nexport { buildObjectExcludingKeys, unshiftForXStatementBody } from \"./util.ts\";\n\n/**\n * Test if a VariableDeclaration's declarations contains any Patterns.\n */\n\nfunction variableDeclarationHasPattern(node: t.VariableDeclaration) {\n  for (const declar of node.declarations) {\n    if (t.isPattern(declar.id)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  loose?: boolean;\n  useBuiltIns?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { useBuiltIns = false } = options;\n\n  const iterableIsArray =\n    api.assumption(\"iterableIsArray\") ?? options.loose ?? false;\n  const arrayLikeIsIterable =\n    options.allowArrayLike ?? api.assumption(\"arrayLikeIsIterable\") ?? false;\n  const objectRestNoSymbols =\n    api.assumption(\"objectRestNoSymbols\") ?? options.loose ?? false;\n\n  return {\n    name: \"transform-destructuring\",\n\n    visitor: {\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n        if (!variableDeclarationHasPattern(declaration.node)) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers())) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n        path.scope.crawl();\n      },\n\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const left = node.left;\n\n        if (t.isPattern(left)) {\n          // for ({ length: k } in { abc: 3 });\n\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n          const statementBody = (path.node.body as t.BlockStatement).body;\n          const nodes = [];\n          // todo: the completion of a for statement can only be observed from\n          // a do block (or eval that we don't support),\n          // but the new do-expression proposal plans to ban iteration ends in the\n          // do block, maybe we can get rid of this\n          if (statementBody.length === 0 && path.isCompletionRecord()) {\n            nodes.unshift(t.expressionStatement(scope.buildUndefinedNode()));\n          }\n\n          nodes.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", left, t.cloneNode(temp)),\n            ),\n          );\n\n          unshiftForXStatementBody(path, nodes);\n          scope.crawl();\n          return;\n        }\n\n        if (!t.isVariableDeclaration(left)) return;\n\n        const pattern = left.declarations[0].id;\n        if (!t.isPattern(pattern)) return;\n\n        const key = scope.generateUidIdentifier(\"ref\");\n        node.left = t.variableDeclaration(left.kind, [\n          t.variableDeclarator(key, null),\n        ]);\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: left.kind,\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n\n        destructuring.init(pattern, key);\n\n        unshiftForXStatementBody(path, nodes);\n        scope.crawl();\n      },\n\n      CatchClause({ node, scope }) {\n        const pattern = node.param;\n        if (!t.isPattern(pattern)) return;\n\n        const ref = scope.generateUidIdentifier(\"ref\");\n        node.param = ref;\n\n        const nodes: DestructuringTransformerNode[] = [];\n\n        const destructuring = new DestructuringTransformer({\n          kind: \"let\",\n          scope: scope,\n          nodes: nodes,\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n          addHelper: name => this.addHelper(name),\n        });\n        destructuring.init(pattern, ref);\n\n        node.body.body = [...nodes, ...node.body.body];\n        scope.crawl();\n      },\n\n      AssignmentExpression(path, state) {\n        if (!t.isPattern(path.node.left)) return;\n        convertAssignmentExpression(\n          path as NodePath<t.AssignmentExpression & { left: t.Pattern }>,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n\n      VariableDeclaration(path, state) {\n        const { node, parent } = path;\n        if (t.isForXStatement(parent)) return;\n        if (!parent || !path.container) return; // i don't know why this is necessary - TODO\n        if (!variableDeclarationHasPattern(node)) return;\n        convertVariableDeclaration(\n          path,\n          name => state.addHelper(name),\n          arrayLikeIsIterable,\n          iterableIsArray,\n          objectRestNoSymbols,\n          useBuiltIns,\n        );\n      },\n    },\n  };\n});\n"], "names": ["isPureVoid", "node", "t", "isUnaryExpression", "operator", "isPureish", "argument", "unshiftForXStatementBody", "statementPath", "newStatements", "ensureBlock", "scope", "bodyScopeBindings", "get", "bindings", "hasShadowedBlockScopedBindings", "Object", "keys", "some", "name", "hasBinding", "body", "blockStatement", "unshift", "hasArrayRest", "pattern", "elements", "elem", "isRestElement", "hasObjectRest", "properties", "prop", "STOP_TRAVERSAL", "arrayUnpackVisitor", "ancestors", "state", "length", "isIdentifier", "isReferenced", "de<PERSON>t", "DestructuringTransformer", "constructor", "opts", "blockHoist", "arrayRefSet", "nodes", "kind", "iterableIsArray", "arrayLikeIsIterable", "objectRestNoSymbols", "useBuiltIns", "addHelper", "Set", "getExtendsHelper", "memberExpression", "identifier", "buildVariableAssignment", "id", "init", "op", "isMemberExpression", "isOptionalMemberExpression", "expressionStatement", "assignmentExpression", "cloneNode", "buildUndefinedNode", "nodeInit", "variableDeclaration", "variableDeclarator", "_blockHoist", "buildVariableDeclaration", "declar", "push", "_init", "isObjectPattern", "pushObjectPattern", "isArrayPattern", "pushArrayPattern", "isAssignmentPattern", "pushAssignmentPattern", "toArray", "count", "has", "left", "right", "valueRef", "tempId", "generateUidIdentifierBasedOnNode", "tempConditional", "conditionalExpression", "binaryExpression", "isPattern", "patternId", "generateUidIdentifier", "pushObjectRest", "objRef", "spreadProp", "spreadPropIndex", "value", "buildObjectExcludingKeys", "slice", "pushObjectProperty", "propRef", "isLiteral", "key", "computed", "callExpression", "isStatic", "temp", "copiedPattern", "i", "isPure", "assign", "canUnpackArrayPattern", "arr", "isArrayExpression", "isSpreadElement", "isCallExpression", "getBindingIdentifiers", "traverse", "e", "pushUnpackedArrayPattern", "holeToUndefined", "el", "arrayExpression", "map", "arrayRef", "add", "elemRef", "numericLiteral", "ref", "memo", "maybeGenerateMemoised", "<PERSON><PERSON><PERSON><PERSON>", "allLiteral", "hasTemplateLiteral", "stringLiteral", "isTemplateLiteral", "String", "isPrivateName", "extendsHelper", "objectExpression", "sequenceExpression", "keyExpression", "isProgram", "block", "programScope", "getProgramParent", "convertVariableDeclaration", "path", "nodeKind", "nodeLoc", "loc", "declarations", "destructuring", "inherits", "tail", "nodesOut", "isVariableDeclaration", "isExpressionStatement", "expression", "expr", "arguments", "isForStatement", "parent", "v", "replaceWith", "replaceWithMultiple", "crawl", "convertAssignmentExpression", "parentPath", "isSequenceExpression", "isCompletionRecord", "isArrowFunctionExpression", "returnStatement", "variableDeclarationHasPattern", "declare", "api", "options", "_ref", "_api$assumption", "_ref2", "_options$allowArrayLi", "_ref3", "_api$assumption2", "assertVersion", "assumption", "loose", "allowArrayLike", "visitor", "ExportNamedDeclaration", "declaration", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "insertAfter", "exportNamedDeclaration", "ForXStatement", "statementBody", "CatchClause", "param", "AssignmentExpression", "VariableDeclaration", "isForXStatement", "container"], "mappings": ";;;;;;;AAGA,SAASA,UAAUA,CAACC,IAAY,EAAE;EAChC,OACEC,UAAC,CAACC,iBAAiB,CAACF,IAAI,CAAC,IACzBA,IAAI,CAACG,QAAQ,KAAK,MAAM,IACxBF,UAAC,CAACG,SAAS,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAA;AAE9B,CAAA;AAEO,SAASC,wBAAwBA,CACtCC,aAAwC,EACxCC,aAA4B,EAC5B;EACAD,aAAa,CAACE,WAAW,EAAE,CAAA;EAC3B,MAAM;IAAEC,KAAK;AAAEV,IAAAA,IAAAA;AAAK,GAAC,GAAGO,aAAa,CAAA;EACrC,MAAMI,iBAAiB,GAAGJ,aAAa,CAACK,GAAG,CAAC,MAAM,CAAC,CAACF,KAAK,CAACG,QAAQ,CAAA;AAClE,EAAA,MAAMC,8BAA8B,GAAGC,MAAM,CAACC,IAAI,CAACL,iBAAiB,CAAC,CAACM,IAAI,CACxEC,IAAI,IAAIR,KAAK,CAACS,UAAU,CAACD,IAAI,CAC/B,CAAC,CAAA;AAED,EAAA,IAAIJ,8BAA8B,EAAE;AAGlCd,IAAAA,IAAI,CAACoB,IAAI,GAAGnB,UAAC,CAACoB,cAAc,CAAC,CAAC,GAAGb,aAAa,EAAER,IAAI,CAACoB,IAAI,CAAC,CAAC,CAAA;AAC7D,GAAC,MAAM;IACJpB,IAAI,CAACoB,IAAI,CAAsBA,IAAI,CAACE,OAAO,CAAC,GAAGd,aAAa,CAAC,CAAA;AAChE,GAAA;AACF,CAAA;AAMA,SAASe,YAAYA,CAACC,OAAuB,EAAE;AAC7C,EAAA,OAAOA,OAAO,CAACC,QAAQ,CAACR,IAAI,CAACS,IAAI,IAAIzB,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,CAAC,CAAA;AAC7D,CAAA;AAMA,SAASE,aAAaA,CAACJ,OAAwB,EAAE;AAC/C,EAAA,OAAOA,OAAO,CAACK,UAAU,CAACZ,IAAI,CAACa,IAAI,IAAI7B,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,CAAC,CAAA;AAC/D,CAAA;AAMA,MAAMC,cAAc,GAAG,EAAE,CAAA;AAQzB,MAAMC,kBAAkB,GAAGA,CACzBhC,IAAY,EACZiC,SAA+B,EAC/BC,KAA8B,KAC3B;AACH,EAAA,IAAI,CAACD,SAAS,CAACE,MAAM,EAAE;AAErB,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IACElC,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IACpBC,UAAC,CAACoC,YAAY,CAACrC,IAAI,EAAEiC,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACnC,IAAI,CAAC,IAC1DkC,KAAK,CAACrB,QAAQ,CAACb,IAAI,CAACkB,IAAI,CAAC,EACzB;IACAgB,KAAK,CAACI,KAAK,GAAG,IAAI,CAAA;AAClB,IAAA,MAAMP,cAAc,CAAA;AACtB,GAAA;AACF,CAAC,CAAA;AAmBM,MAAMQ,wBAAwB,CAAC;EAYpCC,WAAWA,CAACC,IAAoC,EAAE;AAAA,IAAA,IAAA,CAX1CC,UAAU,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACVvC,QAAQ,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CAChBwC,WAAW,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACHC,KAAK,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACLlC,KAAK,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACLmC,IAAI,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACJC,eAAe,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACfC,mBAAmB,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACnBC,mBAAmB,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACnBC,WAAW,GAAA,KAAA,CAAA,CAAA;AAAA,IAAA,IAAA,CACXC,SAAS,GAAA,KAAA,CAAA,CAAA;AAEf,IAAA,IAAI,CAACR,UAAU,GAAGD,IAAI,CAACC,UAAU,CAAA;AACjC,IAAA,IAAI,CAACvC,QAAQ,GAAGsC,IAAI,CAACtC,QAAQ,CAAA;AAC7B,IAAA,IAAI,CAACwC,WAAW,GAAG,IAAIQ,GAAG,EAAE,CAAA;AAC5B,IAAA,IAAI,CAACP,KAAK,GAAGH,IAAI,CAACG,KAAK,IAAI,EAAE,CAAA;AAC7B,IAAA,IAAI,CAAClC,KAAK,GAAG+B,IAAI,CAAC/B,KAAK,CAAA;AACvB,IAAA,IAAI,CAACmC,IAAI,GAAGJ,IAAI,CAACI,IAAI,CAAA;AACrB,IAAA,IAAI,CAACC,eAAe,GAAGL,IAAI,CAACK,eAAe,CAAA;AAC3C,IAAA,IAAI,CAACC,mBAAmB,GAAGN,IAAI,CAACM,mBAAmB,CAAA;AACnD,IAAA,IAAI,CAACC,mBAAmB,GAAGP,IAAI,CAACO,mBAAmB,CAAA;AACnD,IAAA,IAAI,CAACC,WAAW,GAAGR,IAAI,CAACQ,WAAW,CAAA;AACnC,IAAA,IAAI,CAACC,SAAS,GAAGT,IAAI,CAACS,SAAS,CAAA;AACjC,GAAA;AAEAE,EAAAA,gBAAgBA,GAAG;AACjB,IAAA,OAAO,IAAI,CAACH,WAAW,GACnBhD,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE,IAAI,CAACJ,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;AAEAK,EAAAA,uBAAuBA,CACrBC,EAAkC,EAClCC,IAAkB,EAClB;AACA,IAAA,IAAIC,EAAE,GAAG,IAAI,CAACvD,QAAQ,CAAA;AACtB,IAAA,IAAIF,UAAC,CAAC0D,kBAAkB,CAACH,EAAE,CAAC,IAAIvD,UAAC,CAAC2D,0BAA0B,CAACJ,EAAE,CAAC,EAAEE,EAAE,GAAG,GAAG,CAAA;AAE1E,IAAA,IAAI1D,IAAmD,CAAA;AAEvD,IAAA,IAAI0D,EAAE,EAAE;AACN1D,MAAAA,IAAI,GAAGC,UAAC,CAAC4D,mBAAmB,CAC1B5D,UAAC,CAAC6D,oBAAoB,CACpBJ,EAAE,EACFF,EAAE,EACFvD,UAAC,CAAC8D,SAAS,CAACN,IAAI,CAAC,IAAI,IAAI,CAAC/C,KAAK,CAACsD,kBAAkB,EACpD,CACF,CAAC,CAAA;AACH,KAAC,MAAM;AACL,MAAA,IAAIC,QAAsB,CAAA;AAE1B,MAAA,IAAI,CAAC,IAAI,CAACpB,IAAI,KAAK,OAAO,IAAI,IAAI,CAACA,IAAI,KAAK,OAAO,KAAKY,IAAI,KAAK,IAAI,EAAE;AACrEQ,QAAAA,QAAQ,GAAG,IAAI,CAACvD,KAAK,CAACsD,kBAAkB,EAAE,CAAA;AAC5C,OAAC,MAAM;AACLC,QAAAA,QAAQ,GAAGhE,UAAC,CAAC8D,SAAS,CAACN,IAAI,CAAC,CAAA;AAC9B,OAAA;AAEAzD,MAAAA,IAAI,GAAGC,UAAC,CAACiE,mBAAmB,CAAC,IAAI,CAACrB,IAAI,EAAE,CACtC5C,UAAC,CAACkE,kBAAkB,CAACX,EAAE,EAAYS,QAAQ,CAAC,CAC7C,CAAC,CAAA;AACJ,KAAA;AAGAjE,IAAAA,IAAI,CAACoE,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAAA;AAElC,IAAA,OAAO1C,IAAI,CAAA;AACb,GAAA;AAEAqE,EAAAA,wBAAwBA,CAACb,EAAgB,EAAEC,IAAkB,EAAE;AAC7D,IAAA,MAAMa,MAAM,GAAGrE,UAAC,CAACiE,mBAAmB,CAAC,KAAK,EAAE,CAC1CjE,UAAC,CAACkE,kBAAkB,CAAClE,UAAC,CAAC8D,SAAS,CAACP,EAAE,CAAC,EAAEvD,UAAC,CAAC8D,SAAS,CAACN,IAAI,CAAC,CAAC,CACzD,CAAC,CAAA;AAEFa,IAAAA,MAAM,CAACF,WAAW,GAAG,IAAI,CAAC1B,UAAU,CAAA;AACpC,IAAA,OAAO4B,MAAM,CAAA;AACf,GAAA;AAEAC,EAAAA,IAAIA,CAACf,EAAU,EAAEgB,KAA0B,EAAE;AAC3C,IAAA,MAAMf,IAAI,GAAGxD,UAAC,CAAC8D,SAAS,CAACS,KAAK,CAAC,CAAA;AAC/B,IAAA,IAAIvE,UAAC,CAACwE,eAAe,CAACjB,EAAE,CAAC,EAAE;AACzB,MAAA,IAAI,CAACkB,iBAAiB,CAAClB,EAAE,EAAEC,IAAI,CAAC,CAAA;KACjC,MAAM,IAAIxD,UAAC,CAAC0E,cAAc,CAACnB,EAAE,CAAC,EAAE;AAC/B,MAAA,IAAI,CAACoB,gBAAgB,CAACpB,EAAE,EAAEC,IAAI,CAAC,CAAA;KAChC,MAAM,IAAIxD,UAAC,CAAC4E,mBAAmB,CAACrB,EAAE,CAAC,EAAE;AACpC,MAAA,IAAI,CAACsB,qBAAqB,CAACtB,EAAE,EAAEC,IAAI,CAAC,CAAA;AACtC,KAAC,MAAM;AACL,MAAA,IAAI,CAACb,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAACC,EAAE,EAAEC,IAAI,CAAC,CAAC,CAAA;AACzD,KAAA;AACF,GAAA;AAEAsB,EAAAA,OAAOA,CAAC/E,IAAkB,EAAEgF,KAAwB,EAAE;IACpD,IACE,IAAI,CAAClC,eAAe,IACnB7C,UAAC,CAACmC,YAAY,CAACpC,IAAI,CAAC,IAAI,IAAI,CAAC2C,WAAW,CAACsC,GAAG,CAACjF,IAAI,CAACkB,IAAI,CAAE,EACzD;AACA,MAAA,OAAOlB,IAAI,CAAA;AACb,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAACU,KAAK,CAACqE,OAAO,CAAC/E,IAAI,EAAEgF,KAAK,EAAE,IAAI,CAACjC,mBAAmB,CAAC,CAAA;AAClE,KAAA;AACF,GAAA;AAEA+B,EAAAA,qBAAqBA,CACnB;IAAEI,IAAI;AAAEC,IAAAA,KAAAA;GAA4B,EACpCC,QAA6B,EAC7B;AAKA,IAAA,IAAIrF,UAAU,CAACqF,QAAQ,CAAC,EAAE;AACxB,MAAA,IAAI,CAACb,IAAI,CAACW,IAAI,EAAEC,KAAK,CAAC,CAAA;AACtB,MAAA,OAAA;AACF,KAAA;IAIA,MAAME,MAAM,GAAG,IAAI,CAAC3E,KAAK,CAAC4E,gCAAgC,CAACF,QAAQ,CAAC,CAAA;AAEpE,IAAA,IAAI,CAACxC,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACgB,MAAM,EAAED,QAAQ,CAAC,CAAC,CAAA;AAEhE,IAAA,MAAMG,eAAe,GAAGtF,UAAC,CAACuF,qBAAqB,CAC7CvF,UAAC,CAACwF,gBAAgB,CAChB,KAAK,EACLxF,UAAC,CAAC8D,SAAS,CAACsB,MAAM,CAAC,EACnB,IAAI,CAAC3E,KAAK,CAACsD,kBAAkB,EAC/B,CAAC,EACDmB,KAAK,EACLlF,UAAC,CAAC8D,SAAS,CAACsB,MAAM,CACpB,CAAC,CAAA;AAED,IAAA,IAAIpF,UAAC,CAACyF,SAAS,CAACR,IAAI,CAAC,EAAE;AACrB,MAAA,IAAIS,SAAS,CAAA;AACb,MAAA,IAAI3F,IAAI,CAAA;AAER,MAAA,IACE,IAAI,CAAC6C,IAAI,KAAK,OAAO,IACrB,IAAI,CAACA,IAAI,KAAK,KAAK,IACnB,IAAI,CAACA,IAAI,KAAK,OAAO,EACrB;QACA8C,SAAS,GAAG,IAAI,CAACjF,KAAK,CAACkF,qBAAqB,CAACP,MAAM,CAACnE,IAAI,CAAC,CAAA;QACzDlB,IAAI,GAAG,IAAI,CAACqE,wBAAwB,CAACsB,SAAS,EAAEJ,eAAe,CAAC,CAAA;AAClE,OAAC,MAAM;AACLI,QAAAA,SAAS,GAAGN,MAAM,CAAA;QAElBrF,IAAI,GAAGC,UAAC,CAAC4D,mBAAmB,CAC1B5D,UAAC,CAAC6D,oBAAoB,CAAC,GAAG,EAAE7D,UAAC,CAAC8D,SAAS,CAACsB,MAAM,CAAC,EAAEE,eAAe,CAClE,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,IAAI,CAAC3C,KAAK,CAAC2B,IAAI,CAACvE,IAAI,CAAC,CAAA;AACrB,MAAA,IAAI,CAACuE,IAAI,CAACW,IAAI,EAAES,SAAS,CAAC,CAAA;AAC5B,KAAC,MAAM;AACL,MAAA,IAAI,CAAC/C,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAAC2B,IAAI,EAAEK,eAAe,CAAC,CAAC,CAAA;AACtE,KAAA;AACF,GAAA;EAEAM,cAAcA,CACZrE,OAAwB,EACxBsE,MAAoB,EACpBC,UAAyB,EACzBC,eAAuB,EACvB;AACA,IAAA,MAAMC,KAAK,GAAGC,wBAAwB,CACpC1E,OAAO,CAACK,UAAU,CAACsE,KAAK,CAAC,CAAC,EAAEH,eAAe,CAAC,EAC5CF,MAAM,EACN,IAAI,CAACpF,KAAK,EACVQ,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAC,EAC5B,IAAI,CAAC8B,mBAAmB,EACxB,IAAI,CAACC,WACP,CAAC,CAAA;AACD,IAAA,IAAI,CAACL,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAACwC,UAAU,CAAC1F,QAAQ,EAAE4F,KAAK,CAAC,CAAC,CAAA;AAC3E,GAAA;AAEAG,EAAAA,kBAAkBA,CAACtE,IAAsB,EAAEuE,OAAqB,EAAE;AAChE,IAAA,IAAIpG,UAAC,CAACqG,SAAS,CAACxE,IAAI,CAACyE,GAAG,CAAC,EAAEzE,IAAI,CAAC0E,QAAQ,GAAG,IAAI,CAAA;AAE/C,IAAA,MAAMhF,OAAO,GAAGM,IAAI,CAACmE,KAAe,CAAA;IACpC,MAAMH,MAAM,GAAG7F,UAAC,CAACoD,gBAAgB,CAC/BpD,UAAC,CAAC8D,SAAS,CAACsC,OAAO,CAAC,EACpBvE,IAAI,CAACyE,GAAG,EACRzE,IAAI,CAAC0E,QACP,CAAC,CAAA;AAED,IAAA,IAAIvG,UAAC,CAACyF,SAAS,CAAClE,OAAO,CAAC,EAAE;AACxB,MAAA,IAAI,CAAC+C,IAAI,CAAC/C,OAAO,EAAEsE,MAAM,CAAC,CAAA;AAC5B,KAAC,MAAM;AACL,MAAA,IAAI,CAAClD,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAAChB,uBAAuB,CAAC/B,OAAO,EAAEsE,MAAM,CAAC,CAAC,CAAA;AAChE,KAAA;AACF,GAAA;AAEApB,EAAAA,iBAAiBA,CAAClD,OAAwB,EAAEsE,MAAoB,EAAE;AAGhE,IAAA,IAAI,CAACtE,OAAO,CAACK,UAAU,CAACM,MAAM,EAAE;AAC9B,MAAA,IAAI,CAACS,KAAK,CAAC2B,IAAI,CACbtE,UAAC,CAAC4D,mBAAmB,CACnB5D,UAAC,CAACwG,cAAc,CACd,IAAI,CAACvD,SAAS,CAAC,0BAA0B,CAAC,EAC1CnD,UAAU,CAAC+F,MAAM,CAAC,GAAG,EAAE,GAAG,CAACA,MAAM,CACnC,CACF,CACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AAMA,IAAA,IAAItE,OAAO,CAACK,UAAU,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACzB,KAAK,CAACgG,QAAQ,CAACZ,MAAM,CAAC,EAAE;MACjE,MAAMa,IAAI,GAAG,IAAI,CAACjG,KAAK,CAAC4E,gCAAgC,CAACQ,MAAM,CAAC,CAAA;AAChE,MAAA,IAAI,CAAClD,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACsC,IAAI,EAAEb,MAAM,CAAC,CAAC,CAAA;AAC5DA,MAAAA,MAAM,GAAGa,IAAI,CAAA;AACf,KAAA;AAGA,IAAA,IAAI/E,aAAa,CAACJ,OAAO,CAAC,EAAE;AAC1B,MAAA,IAAIoF,aAA8B,CAAA;AAClC,MAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAE0E,CAAC,EAAE,EAAE;AAClD,QAAA,MAAM/E,IAAI,GAAGN,OAAO,CAACK,UAAU,CAACgF,CAAC,CAAC,CAAA;AAClC,QAAA,IAAI5G,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;AACzB,UAAA,MAAA;AACF,SAAA;AACA,QAAA,MAAMyE,GAAG,GAAGzE,IAAI,CAACyE,GAAG,CAAA;AACpB,QAAA,IAAIzE,IAAI,CAAC0E,QAAQ,IAAI,CAAC,IAAI,CAAC9F,KAAK,CAACoG,MAAM,CAACP,GAAG,CAAC,EAAE;UAC5C,MAAMrF,IAAI,GAAG,IAAI,CAACR,KAAK,CAAC4E,gCAAgC,CAACiB,GAAG,CAAC,CAAA;AAC7D,UAAA,IAAI,CAAC3D,KAAK,CAAC2B,IAAI,CAEb,IAAI,CAACF,wBAAwB,CAACnD,IAAI,EAAEqF,GAAG,CACzC,CAAC,CAAA;UACD,IAAI,CAACK,aAAa,EAAE;AAClBA,YAAAA,aAAa,GAAGpF,OAAO,GAAAT,MAAA,CAAAgG,MAAA,KAClBvF,OAAO,EAAA;AACVK,cAAAA,UAAU,EAAEL,OAAO,CAACK,UAAU,CAACsE,KAAK,EAAC;aACtC,CAAA,CAAA;AACH,WAAA;UACAS,aAAa,CAAC/E,UAAU,CAACgF,CAAC,CAAC,GAAA9F,MAAA,CAAAgG,MAAA,CAAA,EAAA,EACtBjF,IAAI,EAAA;AACPyE,YAAAA,GAAG,EAAErF,IAAAA;WACN,CAAA,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,OAAO,CAACK,UAAU,CAACM,MAAM,EAAE0E,CAAC,EAAE,EAAE;AAClD,MAAA,MAAM/E,IAAI,GAAGN,OAAO,CAACK,UAAU,CAACgF,CAAC,CAAC,CAAA;AAClC,MAAA,IAAI5G,UAAC,CAAC0B,aAAa,CAACG,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC+D,cAAc,CAACrE,OAAO,EAAEsE,MAAM,EAAEhE,IAAI,EAAE+E,CAAC,CAAC,CAAA;AAC/C,OAAC,MAAM;AACL,QAAA,IAAI,CAACT,kBAAkB,CAACtE,IAAI,EAAEgE,MAAM,CAAC,CAAA;AACvC,OAAA;AACF,KAAA;AACF,GAAA;AAEAkB,EAAAA,qBAAqBA,CACnBxF,OAAuB,EACvByF,GAAiB,EACiB;IAElC,IAAI,CAAChH,UAAC,CAACiH,iBAAiB,CAACD,GAAG,CAAC,EAAE,OAAO,KAAK,CAAA;IAI3C,IAAIzF,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG8E,GAAG,CAACxF,QAAQ,CAACU,MAAM,EAAE,OAAA;AACnD,IAAA,IACEX,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG8E,GAAG,CAACxF,QAAQ,CAACU,MAAM,IAC7C,CAACZ,YAAY,CAACC,OAAO,CAAC,EACtB;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,KAAK,MAAME,IAAI,IAAIF,OAAO,CAACC,QAAQ,EAAE;AAEnC,MAAA,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK,CAAA;MAGvB,IAAIzB,UAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAC9C,KAAA;AAEA,IAAA,KAAK,MAAMA,IAAI,IAAIuF,GAAG,CAACxF,QAAQ,EAAE;MAE/B,IAAIxB,UAAC,CAACkH,eAAe,CAACzF,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;MAGzC,IAAIzB,UAAC,CAACmH,gBAAgB,CAAC1F,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;MAG1C,IAAIzB,UAAC,CAAC0D,kBAAkB,CAACjC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAC9C,KAAA;AAGA,IAAA,MAAMb,QAAQ,GAAGZ,UAAC,CAACoH,qBAAqB,CAAC7F,OAAO,CAAC,CAAA;AACjD,IAAA,MAAMU,KAA8B,GAAG;AAAEI,MAAAA,KAAK,EAAE,KAAK;AAAEzB,MAAAA,QAAAA;KAAU,CAAA;IAEjE,IAAI;MACFZ,UAAC,CAACqH,QAAQ,CAACL,GAAG,EAAEjF,kBAAkB,EAAEE,KAAK,CAAC,CAAA;KAC3C,CAAC,OAAOqF,CAAC,EAAE;AACV,MAAA,IAAIA,CAAC,KAAKxF,cAAc,EAAE,MAAMwF,CAAC,CAAA;AACnC,KAAA;IAEA,OAAO,CAACrF,KAAK,CAACI,KAAK,CAAA;AACrB,GAAA;AAEAkF,EAAAA,wBAAwBA,CACtBhG,OAAuB,EACvByF,GAA8B,EAC9B;AACA,IAAA,MAAMQ,eAAe,GAAIC,EAAgB,IACvCA,EAAE,IAAFA,IAAAA,GAAAA,EAAE,GAAI,IAAI,CAAChH,KAAK,CAACsD,kBAAkB,EAAE,CAAA;AAEvC,IAAA,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAE0E,CAAC,EAAE,EAAE;AAChD,MAAA,MAAMnF,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACoF,CAAC,CAAC,CAAA;AAChC,MAAA,IAAI5G,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC6C,IAAI,CACP7C,IAAI,CAACrB,QAAQ,EACbJ,UAAC,CAAC0H,eAAe,CAACV,GAAG,CAACxF,QAAQ,CAAC0E,KAAK,CAACU,CAAC,CAAC,CAACe,GAAG,CAACH,eAAe,CAAC,CAC9D,CAAC,CAAA;AACH,OAAC,MAAM;AACL,QAAA,IAAI,CAAClD,IAAI,CAAC7C,IAAI,EAAE+F,eAAe,CAACR,GAAG,CAACxF,QAAQ,CAACoF,CAAC,CAAC,CAAC,CAAC,CAAA;AACnD,OAAA;AACF,KAAA;AACF,GAAA;AAEAjC,EAAAA,gBAAgBA,CAACpD,OAAuB,EAAEqG,QAA6B,EAAE;IACvE,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAACjF,KAAK,CAAC2B,IAAI,CACbtE,UAAC,CAAC4D,mBAAmB,CACnB5D,UAAC,CAACwG,cAAc,CAAC,IAAI,CAACvD,SAAS,CAAC,0BAA0B,CAAC,EAAE,EAAE,CACjE,CACF,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AACA,IAAA,IAAI,CAAC1B,OAAO,CAACC,QAAQ,EAAE,OAAA;IASvB,IAAI,IAAI,CAACuF,qBAAqB,CAACxF,OAAO,EAAEqG,QAAQ,CAAC,EAAE;AACjD,MAAA,IAAI,CAACL,wBAAwB,CAAChG,OAAO,EAAEqG,QAAQ,CAAC,CAAA;AAChD,MAAA,OAAA;AACF,KAAA;AAKA,IAAA,MAAM7C,KAAK,GAAG,CAACzD,YAAY,CAACC,OAAO,CAAC,IAAIA,OAAO,CAACC,QAAQ,CAACU,MAAM,CAAA;IAM/D,MAAM4C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8C,QAAQ,EAAE7C,KAAK,CAAC,CAAA;AAE7C,IAAA,IAAI/E,UAAC,CAACmC,YAAY,CAAC2C,OAAO,CAAC,EAAE;AAG3B8C,MAAAA,QAAQ,GAAG9C,OAAO,CAAA;AACpB,KAAC,MAAM;MACL8C,QAAQ,GAAG,IAAI,CAACnH,KAAK,CAAC4E,gCAAgC,CAACuC,QAAQ,CAAC,CAAA;MAChE,IAAI,CAAClF,WAAW,CAACmF,GAAG,CAACD,QAAQ,CAAC3G,IAAI,CAAC,CAAA;AACnC,MAAA,IAAI,CAAC0B,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAACwD,QAAQ,EAAE9C,OAAO,CAAC,CAAC,CAAA;AACnE,KAAA;AAEA,IAAA,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,OAAO,CAACC,QAAQ,CAACU,MAAM,EAAE0E,CAAC,EAAE,EAAE;AAChD,MAAA,MAAMnF,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAACoF,CAAC,CAAC,CAAA;MAGhC,IAAI,CAACnF,IAAI,EAAE,SAAA;AAEX,MAAA,IAAIqG,OAAO,CAAA;AAEX,MAAA,IAAI9H,UAAC,CAAC0B,aAAa,CAACD,IAAI,CAAC,EAAE;AACzBqG,QAAAA,OAAO,GAAG,IAAI,CAAChD,OAAO,CAAC8C,QAAQ,CAAC,CAAA;QAChCE,OAAO,GAAG9H,UAAC,CAACwG,cAAc,CACxBxG,UAAC,CAACoD,gBAAgB,CAAC0E,OAAO,EAAE9H,UAAC,CAACqD,UAAU,CAAC,OAAO,CAAC,CAAC,EAClD,CAACrD,UAAC,CAAC+H,cAAc,CAACnB,CAAC,CAAC,CACtB,CAAC,CAAA;QAID,IAAI,CAACtC,IAAI,CAAC7C,IAAI,CAACrB,QAAQ,EAAE0H,OAAO,CAAC,CAAA;AACnC,OAAC,MAAM;AACLA,QAAAA,OAAO,GAAG9H,UAAC,CAACoD,gBAAgB,CAACwE,QAAQ,EAAE5H,UAAC,CAAC+H,cAAc,CAACnB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACjE,QAAA,IAAI,CAACtC,IAAI,CAAC7C,IAAI,EAAEqG,OAAO,CAAC,CAAA;AAC1B,OAAA;AACF,KAAA;AACF,GAAA;AAEAtE,EAAAA,IAAIA,CAACjC,OAAe,EAAEyG,GAAiB,EAAE;AAIvC,IAAA,IAAI,CAAChI,UAAC,CAACiH,iBAAiB,CAACe,GAAG,CAAC,IAAI,CAAChI,UAAC,CAAC0D,kBAAkB,CAACsE,GAAG,CAAC,EAAE;MAC3D,MAAMC,IAAI,GAAG,IAAI,CAACxH,KAAK,CAACyH,qBAAqB,CAACF,GAAG,EAAE,IAAI,CAAC,CAAA;AACxD,MAAA,IAAIC,IAAI,EAAE;AACR,QAAA,IAAI,CAACtF,KAAK,CAAC2B,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAAC6D,IAAI,EAAEjI,UAAC,CAAC8D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC,CAAA;AACtEA,QAAAA,GAAG,GAAGC,IAAI,CAAA;AACZ,OAAA;AACF,KAAA;AAEA,IAAA,IAAI,CAAC3D,IAAI,CAAC/C,OAAO,EAAEyG,GAAG,CAAC,CAAA;IAEvB,OAAO,IAAI,CAACrF,KAAK,CAAA;AACnB,GAAA;AACF,CAAA;AAOO,SAASsD,wBAAwBA,CACtCkC,YAAiB,EACjBtC,MAAoB,EACpBpF,KAAY,EACZwC,SAA4B,EAC5BF,mBAA4B,EAC5BC,WAAoB,EACF;EAGlB,MAAMjC,IAAI,GAAG,EAAE,CAAA;EACf,IAAIqH,UAAU,GAAG,IAAI,CAAA;EACrB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAC9B,EAAA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,YAAY,CAACjG,MAAM,EAAE0E,CAAC,EAAE,EAAE;AAC5C,IAAA,MAAM/E,IAAI,GAAGsG,YAAY,CAACvB,CAAC,CAAC,CAAA;AAC5B,IAAA,MAAMN,GAAG,GAAGzE,IAAI,CAACyE,GAAG,CAAA;IACpB,IAAItG,UAAC,CAACmC,YAAY,CAACmE,GAAG,CAAC,IAAI,CAACzE,IAAI,CAAC0E,QAAQ,EAAE;MACzCxF,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAACsI,aAAa,CAAChC,GAAG,CAACrF,IAAI,CAAC,CAAC,CAAA;KACrC,MAAM,IAAIjB,UAAC,CAACuI,iBAAiB,CAACjC,GAAG,CAAC,EAAE;MACnCvF,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAAC8D,SAAS,CAACwC,GAAG,CAAC,CAAC,CAAA;AAC3B+B,MAAAA,kBAAkB,GAAG,IAAI,CAAA;KAC1B,MAAM,IAAIrI,UAAC,CAACqG,SAAS,CAACC,GAAG,CAAC,EAAE;AAE3BvF,MAAAA,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAACsI,aAAa,CAACE,MAAM,CAAClC,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,CAAA;KAC9C,MAAM,IAAIhG,UAAC,CAACyI,aAAa,CAACnC,GAAG,CAAC,EAAE,CAEhC,MAAM;MACLvF,IAAI,CAACuD,IAAI,CAACtE,UAAC,CAAC8D,SAAS,CAACwC,GAAG,CAAC,CAAC,CAAA;AAC3B8B,MAAAA,UAAU,GAAG,KAAK,CAAA;AACpB,KAAA;AACF,GAAA;AAEA,EAAA,IAAIpC,KAAK,CAAA;AACT,EAAA,IAAIjF,IAAI,CAACmB,MAAM,KAAK,CAAC,EAAE;IACrB,MAAMwG,aAAa,GAAG1F,WAAW,GAC7BhD,UAAC,CAACoD,gBAAgB,CAACpD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,EAAErD,UAAC,CAACqD,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClEJ,SAAS,CAAC,SAAS,CAAC,CAAA;IACxB+C,KAAK,GAAGhG,UAAC,CAACwG,cAAc,CAACkC,aAAa,EAAE,CACtC1I,UAAC,CAAC2I,gBAAgB,CAAC,EAAE,CAAC,EACtB3I,UAAC,CAAC4I,kBAAkB,CAAC,CACnB5I,UAAC,CAACwG,cAAc,CAACvD,SAAS,CAAC,0BAA0B,CAAC,EAAE,CACtDjD,UAAC,CAAC8D,SAAS,CAAC+B,MAAM,CAAC,CACpB,CAAC,EACF7F,UAAC,CAAC8D,SAAS,CAAC+B,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CAAA;AACJ,GAAC,MAAM;AACL,IAAA,IAAIgD,aAA2B,GAAG7I,UAAC,CAAC0H,eAAe,CAAC3G,IAAI,CAAC,CAAA;IAEzD,IAAI,CAACqH,UAAU,EAAE;MACfS,aAAa,GAAG7I,UAAC,CAACwG,cAAc,CAC9BxG,UAAC,CAACoD,gBAAgB,CAACyF,aAAa,EAAE7I,UAAC,CAACqD,UAAU,CAAC,KAAK,CAAC,CAAC,EACtD,CAACJ,SAAS,CAAC,eAAe,CAAC,CAC7B,CAAC,CAAA;AACH,KAAC,MAAM,IAAI,CAACoF,kBAAkB,IAAI,CAACrI,UAAC,CAAC8I,SAAS,CAACrI,KAAK,CAACsI,KAAK,CAAC,EAAE;AAE3D,MAAA,MAAMC,YAAY,GAAGvI,KAAK,CAACwI,gBAAgB,EAAE,CAAA;AAC7C,MAAA,MAAM1F,EAAE,GAAGyF,YAAY,CAACrD,qBAAqB,CAAC,UAAU,CAAC,CAAA;MAEzDqD,YAAY,CAAC1E,IAAI,CAAC;QAChBf,EAAE;AACFC,QAAAA,IAAI,EAAEqF,aAAa;AACnBjG,QAAAA,IAAI,EAAE,OAAA;AACR,OAAC,CAAC,CAAA;AAEFiG,MAAAA,aAAa,GAAG7I,UAAC,CAAC8D,SAAS,CAACP,EAAE,CAAC,CAAA;AACjC,KAAA;IAEAyC,KAAK,GAAGhG,UAAC,CAACwG,cAAc,CACtBvD,SAAS,CAAE,CAAyBF,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAG,EAAC,CAAC,EACzE,CAAC/C,UAAC,CAAC8D,SAAS,CAAC+B,MAAM,CAAC,EAAEgD,aAAa,CACrC,CAAC,CAAA;AACH,GAAA;AACA,EAAA,OAAO7C,KAAK,CAAA;AACd,CAAA;AAEO,SAASkD,0BAA0BA,CACxCC,IAAqC,EACrClG,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;AAAEU,IAAAA,KAAAA;AAAM,GAAC,GAAG0I,IAAI,CAAA;AAE5B,EAAA,MAAMC,QAAQ,GAAGrJ,IAAI,CAAC6C,IAAI,CAAA;AAC1B,EAAA,MAAMyG,OAAO,GAAGtJ,IAAI,CAACuJ,GAAG,CAAA;EACxB,MAAM3G,KAAK,GAAG,EAAE,CAAA;AAEhB,EAAA,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7G,IAAI,CAACwJ,YAAY,CAACrH,MAAM,EAAE0E,CAAC,EAAE,EAAE;AACjD,IAAA,MAAMvC,MAAM,GAAGtE,IAAI,CAACwJ,YAAY,CAAC3C,CAAC,CAAC,CAAA;AAEnC,IAAA,MAAMlB,SAAS,GAAGrB,MAAM,CAACb,IAAI,CAAA;AAC7B,IAAA,MAAMjC,OAAO,GAAG8C,MAAM,CAACd,EAAE,CAAA;AAEzB,IAAA,MAAMiG,aAAuC,GAC3C,IAAIlH,wBAAwB,CAAC;MAE3BG,UAAU,EAAE1C,IAAI,CAACoE,WAAW;AAC5BxB,MAAAA,KAAK,EAAEA,KAAK;AACZlC,MAAAA,KAAK,EAAEA,KAAK;MACZmC,IAAI,EAAE7C,IAAI,CAAC6C,IAAI;MACfC,eAAe;MACfC,mBAAmB;MACnBE,WAAW;MACXD,mBAAmB;AACnBE,MAAAA,SAAAA;AACF,KAAC,CAAC,CAAA;AAEJ,IAAA,IAAIjD,UAAC,CAACyF,SAAS,CAAClE,OAAO,CAAC,EAAE;AACxBiI,MAAAA,aAAa,CAAChG,IAAI,CAACjC,OAAO,EAAEmE,SAAS,CAAC,CAAA;MAEtC,IAAI,CAACkB,CAAC,KAAK7G,IAAI,CAACwJ,YAAY,CAACrH,MAAM,GAAG,CAAC,EAAE;AAGvClC,QAAAA,UAAC,CAACyJ,QAAQ,CAAC9G,KAAK,CAACA,KAAK,CAACT,MAAM,GAAG,CAAC,CAAC,EAAEmC,MAAM,CAAC,CAAA;AAC7C,OAAA;AACF,KAAC,MAAM;AACL1B,MAAAA,KAAK,CAAC2B,IAAI,CACRtE,UAAC,CAACyJ,QAAQ,CACRD,aAAa,CAAClG,uBAAuB,CAAC/B,OAAO,EAAEmE,SAAS,CAAC,EACzDrB,MACF,CACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;EAEA,IAAIqF,IAAkC,GAAG,IAAI,CAAA;EAC7C,IAAIC,QAAQ,GAAG,EAAE,CAAA;AACjB,EAAA,KAAK,MAAM5J,IAAI,IAAI4C,KAAK,EAAE;AACxB,IAAA,IAAI3C,UAAC,CAAC4J,qBAAqB,CAAC7J,IAAI,CAAC,EAAE;MACjC,IAAI2J,IAAI,KAAK,IAAI,EAAE;QAEjBA,IAAI,CAACH,YAAY,CAACjF,IAAI,CAAC,GAAGvE,IAAI,CAACwJ,YAAY,CAAC,CAAA;AAC5C,QAAA,SAAA;AACF,OAAC,MAAM;QAELxJ,IAAI,CAAC6C,IAAI,GAAGwG,QAAQ,CAAA;AACpBM,QAAAA,IAAI,GAAG3J,IAAI,CAAA;AACb,OAAA;AACF,KAAC,MAAM;AACL2J,MAAAA,IAAI,GAAG,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,IAAI,CAAC3J,IAAI,CAACuJ,GAAG,EAAE;MACbvJ,IAAI,CAACuJ,GAAG,GAAGD,OAAO,CAAA;AACpB,KAAA;AACAM,IAAAA,QAAQ,CAACrF,IAAI,CAACvE,IAAI,CAAC,CAAA;AACrB,GAAA;EAEA,IACE4J,QAAQ,CAACzH,MAAM,KAAK,CAAC,IACrBlC,UAAC,CAAC4J,qBAAqB,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpC3J,UAAC,CAAC6J,qBAAqB,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,IACpC3J,UAAC,CAACmH,gBAAgB,CAACwC,QAAQ,CAAC,CAAC,CAAC,CAACG,UAAU,CAAC,IAC1CH,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAACrH,MAAM,KAAK,CAAC,EACrC;AAOA,IAAA,MAAM6H,IAAI,GAAGJ,QAAQ,CAAC,CAAC,CAAC,CAACG,UAAU,CAAA;AACnCC,IAAAA,IAAI,CAACC,SAAS,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC/F,IAAI,CAAC,CAAA;IACnDmG,QAAQ,GAAG,CAACI,IAAI,CAAC,CAAA;AACnB,GAAC,MAAM;AAEL,IAAA,IACE/J,UAAC,CAACiK,cAAc,CAACd,IAAI,CAACe,MAAM,EAAE;AAAE1G,MAAAA,IAAI,EAAEzD,IAAAA;AAAK,KAAC,CAAC,IAC7C,CAAC4J,QAAQ,CAAC3I,IAAI,CAACmJ,CAAC,IAAInK,UAAC,CAAC4J,qBAAqB,CAACO,CAAC,CAAC,CAAC,EAC/C;AACA,MAAA,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,QAAQ,CAACzH,MAAM,EAAE0E,CAAC,EAAE,EAAE;AACxC,QAAA,MAAM7G,IAAY,GAAG4J,QAAQ,CAAC/C,CAAC,CAAC,CAAA;AAChC,QAAA,IAAI5G,UAAC,CAAC6J,qBAAqB,CAAC9J,IAAI,CAAC,EAAE;AACjC4J,UAAAA,QAAQ,CAAC/C,CAAC,CAAC,GAAG7G,IAAI,CAAC+J,UAAU,CAAA;AAC/B,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAEA,EAAA,IAAIH,QAAQ,CAACzH,MAAM,KAAK,CAAC,EAAE;AACzBiH,IAAAA,IAAI,CAACiB,WAAW,CAACT,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/B,GAAC,MAAM;AACLR,IAAAA,IAAI,CAACkB,mBAAmB,CAACV,QAAQ,CAAC,CAAA;AACpC,GAAA;EACAlJ,KAAK,CAAC6J,KAAK,EAAE,CAAA;AACf,CAAA;AAEO,SAASC,2BAA2BA,CACzCpB,IAA4D,EAC5DlG,SAA4B,EAC5BH,mBAA4B,EAC5BD,eAAwB,EACxBE,mBAA4B,EAC5BC,WAAoB,EACpB;EACA,MAAM;IAAEjD,IAAI;IAAEU,KAAK;AAAE+J,IAAAA,UAAAA;AAAW,GAAC,GAAGrB,IAAI,CAAA;EAExC,MAAMxG,KAAqC,GAAG,EAAE,CAAA;AAEhD,EAAA,MAAM6G,aAAa,GAAG,IAAIlH,wBAAwB,CAAC;IACjDpC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;AACvBO,IAAAA,KAAK,EAAEA,KAAK;AACZkC,IAAAA,KAAK,EAAEA,KAAK;IACZG,mBAAmB;IACnBD,eAAe;IACfE,mBAAmB;IACnBC,WAAW;AACXC,IAAAA,SAAAA;AACF,GAAC,CAAC,CAAA;AAEF,EAAA,IAAI+E,GAAwB,CAAA;AAC5B,EAAA,IACG,CAACwC,UAAU,CAACX,qBAAqB,EAAE,IAClC,CAACW,UAAU,CAACC,oBAAoB,EAAE,IACpCtB,IAAI,CAACuB,kBAAkB,EAAE,EACzB;IACA1C,GAAG,GAAGvH,KAAK,CAAC4E,gCAAgC,CAACtF,IAAI,CAACmF,KAAK,EAAE,KAAK,CAAC,CAAA;IAE/DvC,KAAK,CAAC2B,IAAI,CACRtE,UAAC,CAACiE,mBAAmB,CAAC,KAAK,EAAE,CAACjE,UAAC,CAACkE,kBAAkB,CAAC8D,GAAG,EAAEjI,IAAI,CAACmF,KAAK,CAAC,CAAC,CACtE,CAAC,CAAA;IAED,IAAIlF,UAAC,CAACiH,iBAAiB,CAAClH,IAAI,CAACmF,KAAK,CAAC,EAAE;MACnCsE,aAAa,CAAC9G,WAAW,CAACmF,GAAG,CAACG,GAAG,CAAC/G,IAAI,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;AAEAuI,EAAAA,aAAa,CAAChG,IAAI,CAACzD,IAAI,CAACkF,IAAI,EAAE+C,GAAG,IAAIjI,IAAI,CAACmF,KAAK,CAAC,CAAA;AAEhD,EAAA,IAAI8C,GAAG,EAAE;AACP,IAAA,IAAIwC,UAAU,CAACG,yBAAyB,EAAE,EAAE;MAC1CxB,IAAI,CAACiB,WAAW,CAACpK,UAAC,CAACoB,cAAc,CAAC,EAAE,CAAC,CAAC,CAAA;AACtCuB,MAAAA,KAAK,CAAC2B,IAAI,CAACtE,UAAC,CAAC4K,eAAe,CAAC5K,UAAC,CAAC8D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC,CAAA;AACjD,KAAC,MAAM;AACLrF,MAAAA,KAAK,CAAC2B,IAAI,CAACtE,UAAC,CAAC4D,mBAAmB,CAAC5D,UAAC,CAAC8D,SAAS,CAACkE,GAAG,CAAC,CAAC,CAAC,CAAA;AACrD,KAAA;AACF,GAAA;AAEAmB,EAAAA,IAAI,CAACkB,mBAAmB,CAAC1H,KAAK,CAAC,CAAA;EAC/BlC,KAAK,CAAC6J,KAAK,EAAE,CAAA;AACf;;ACpuBA,SAASO,6BAA6BA,CAAC9K,IAA2B,EAAE;AAClE,EAAA,KAAK,MAAMsE,MAAM,IAAItE,IAAI,CAACwJ,YAAY,EAAE;IACtC,IAAIvJ,UAAC,CAACyF,SAAS,CAACpB,MAAM,CAACd,EAAE,CAAC,EAAE;AAC1B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAQA,YAAeuH,yBAAO,CAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,IAAA,EAAAC,eAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,gBAAA,CAAA;AAChDP,EAAAA,GAAG,CAACQ,aAAa,CAAkB,CAAE,CAAC,CAAA;EAEtC,MAAM;AAAEvI,IAAAA,WAAW,GAAG,KAAA;AAAM,GAAC,GAAGgI,OAAO,CAAA;EAEvC,MAAMnI,eAAe,IAAAoI,IAAA,GAAA,CAAAC,eAAA,GACnBH,GAAG,CAACS,UAAU,CAAC,iBAAiB,CAAC,KAAA,IAAA,GAAAN,eAAA,GAAIF,OAAO,CAACS,KAAK,KAAA,IAAA,GAAAR,IAAA,GAAI,KAAK,CAAA;EAC7D,MAAMnI,mBAAmB,IAAAqI,KAAA,GAAA,CAAAC,qBAAA,GACvBJ,OAAO,CAACU,cAAc,KAAA,IAAA,GAAAN,qBAAA,GAAIL,GAAG,CAACS,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAAL,KAAA,GAAI,KAAK,CAAA;EAC1E,MAAMpI,mBAAmB,IAAAsI,KAAA,GAAA,CAAAC,gBAAA,GACvBP,GAAG,CAACS,UAAU,CAAC,qBAAqB,CAAC,KAAA,IAAA,GAAAF,gBAAA,GAAIN,OAAO,CAACS,KAAK,KAAA,IAAA,GAAAJ,KAAA,GAAI,KAAK,CAAA;EAEjE,OAAO;AACLpK,IAAAA,IAAI,EAAE,yBAAyB;AAE/B0K,IAAAA,OAAO,EAAE;MACPC,sBAAsBA,CAACzC,IAAI,EAAE;AAC3B,QAAA,MAAM0C,WAAW,GAAG1C,IAAI,CAACxI,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAACkL,WAAW,CAACjC,qBAAqB,EAAE,EAAE,OAAA;AAC1C,QAAA,IAAI,CAACiB,6BAA6B,CAACgB,WAAW,CAAC9L,IAAI,CAAC,EAAE,OAAA;QAEtD,MAAM+L,UAAU,GAAG,EAAE,CAAA;AAErB,QAAA,KAAK,MAAM7K,IAAI,IAAIH,MAAM,CAACC,IAAI,CAACoI,IAAI,CAAC4C,0BAA0B,EAAE,CAAC,EAAE;UACjED,UAAU,CAACxH,IAAI,CACbtE,UAAC,CAACgM,eAAe,CAAChM,UAAC,CAACqD,UAAU,CAACpC,IAAI,CAAC,EAAEjB,UAAC,CAACqD,UAAU,CAACpC,IAAI,CAAC,CAC1D,CAAC,CAAA;AACH,SAAA;AAKAkI,QAAAA,IAAI,CAACiB,WAAW,CAACyB,WAAW,CAAC9L,IAAI,CAAC,CAAA;QAClCoJ,IAAI,CAAC8C,WAAW,CAACjM,UAAC,CAACkM,sBAAsB,CAAC,IAAI,EAAEJ,UAAU,CAAC,CAAC,CAAA;AAC5D3C,QAAAA,IAAI,CAAC1I,KAAK,CAAC6J,KAAK,EAAE,CAAA;OACnB;MAED6B,aAAaA,CAAChD,IAA+B,EAAE;QAC7C,MAAM;UAAEpJ,IAAI;AAAEU,UAAAA,KAAAA;AAAM,SAAC,GAAG0I,IAAI,CAAA;AAC5B,QAAA,MAAMlE,IAAI,GAAGlF,IAAI,CAACkF,IAAI,CAAA;AAEtB,QAAA,IAAIjF,UAAC,CAACyF,SAAS,CAACR,IAAI,CAAC,EAAE;AAGrB,UAAA,MAAMyB,IAAI,GAAGjG,KAAK,CAACkF,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/C5F,UAAAA,IAAI,CAACkF,IAAI,GAAGjF,UAAC,CAACiE,mBAAmB,CAAC,KAAK,EAAE,CACvCjE,UAAC,CAACkE,kBAAkB,CAACwC,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEFyC,IAAI,CAAC3I,WAAW,EAAE,CAAA;UAClB,MAAM4L,aAAa,GAAIjD,IAAI,CAACpJ,IAAI,CAACoB,IAAI,CAAsBA,IAAI,CAAA;UAC/D,MAAMwB,KAAK,GAAG,EAAE,CAAA;UAKhB,IAAIyJ,aAAa,CAAClK,MAAM,KAAK,CAAC,IAAIiH,IAAI,CAACuB,kBAAkB,EAAE,EAAE;AAC3D/H,YAAAA,KAAK,CAACtB,OAAO,CAACrB,UAAC,CAAC4D,mBAAmB,CAACnD,KAAK,CAACsD,kBAAkB,EAAE,CAAC,CAAC,CAAA;AAClE,WAAA;UAEApB,KAAK,CAACtB,OAAO,CACXrB,UAAC,CAAC4D,mBAAmB,CACnB5D,UAAC,CAAC6D,oBAAoB,CAAC,GAAG,EAAEoB,IAAI,EAAEjF,UAAC,CAAC8D,SAAS,CAAC4C,IAAI,CAAC,CACrD,CACF,CAAC,CAAA;AAEDrG,UAAAA,wBAAwB,CAAC8I,IAAI,EAAExG,KAAK,CAAC,CAAA;UACrClC,KAAK,CAAC6J,KAAK,EAAE,CAAA;AACb,UAAA,OAAA;AACF,SAAA;AAEA,QAAA,IAAI,CAACtK,UAAC,CAAC4J,qBAAqB,CAAC3E,IAAI,CAAC,EAAE,OAAA;QAEpC,MAAM1D,OAAO,GAAG0D,IAAI,CAACsE,YAAY,CAAC,CAAC,CAAC,CAAChG,EAAE,CAAA;AACvC,QAAA,IAAI,CAACvD,UAAC,CAACyF,SAAS,CAAClE,OAAO,CAAC,EAAE,OAAA;AAE3B,QAAA,MAAM+E,GAAG,GAAG7F,KAAK,CAACkF,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC9C5F,IAAI,CAACkF,IAAI,GAAGjF,UAAC,CAACiE,mBAAmB,CAACgB,IAAI,CAACrC,IAAI,EAAE,CAC3C5C,UAAC,CAACkE,kBAAkB,CAACoC,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;QAEF,MAAM3D,KAAqC,GAAG,EAAE,CAAA;AAEhD,QAAA,MAAM6G,aAAa,GAAG,IAAIlH,wBAAwB,CAAC;UACjDM,IAAI,EAAEqC,IAAI,CAACrC,IAAI;AACfnC,UAAAA,KAAK,EAAEA,KAAK;AACZkC,UAAAA,KAAK,EAAEA,KAAK;UACZG,mBAAmB;UACnBD,eAAe;UACfE,mBAAmB;UACnBC,WAAW;AACXC,UAAAA,SAAS,EAAEhC,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAA;AACxC,SAAC,CAAC,CAAA;AAEFuI,QAAAA,aAAa,CAAChG,IAAI,CAACjC,OAAO,EAAE+E,GAAG,CAAC,CAAA;AAEhCjG,QAAAA,wBAAwB,CAAC8I,IAAI,EAAExG,KAAK,CAAC,CAAA;QACrClC,KAAK,CAAC6J,KAAK,EAAE,CAAA;OACd;AAED+B,MAAAA,WAAWA,CAAC;QAAEtM,IAAI;AAAEU,QAAAA,KAAAA;AAAM,OAAC,EAAE;AAC3B,QAAA,MAAMc,OAAO,GAAGxB,IAAI,CAACuM,KAAK,CAAA;AAC1B,QAAA,IAAI,CAACtM,UAAC,CAACyF,SAAS,CAAClE,OAAO,CAAC,EAAE,OAAA;AAE3B,QAAA,MAAMyG,GAAG,GAAGvH,KAAK,CAACkF,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAC9C5F,IAAI,CAACuM,KAAK,GAAGtE,GAAG,CAAA;QAEhB,MAAMrF,KAAqC,GAAG,EAAE,CAAA;AAEhD,QAAA,MAAM6G,aAAa,GAAG,IAAIlH,wBAAwB,CAAC;AACjDM,UAAAA,IAAI,EAAE,KAAK;AACXnC,UAAAA,KAAK,EAAEA,KAAK;AACZkC,UAAAA,KAAK,EAAEA,KAAK;UACZG,mBAAmB;UACnBD,eAAe;UACfE,mBAAmB;UACnBC,WAAW;AACXC,UAAAA,SAAS,EAAEhC,IAAI,IAAI,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAA;AACxC,SAAC,CAAC,CAAA;AACFuI,QAAAA,aAAa,CAAChG,IAAI,CAACjC,OAAO,EAAEyG,GAAG,CAAC,CAAA;AAEhCjI,QAAAA,IAAI,CAACoB,IAAI,CAACA,IAAI,GAAG,CAAC,GAAGwB,KAAK,EAAE,GAAG5C,IAAI,CAACoB,IAAI,CAACA,IAAI,CAAC,CAAA;QAC9CV,KAAK,CAAC6J,KAAK,EAAE,CAAA;OACd;AAEDiC,MAAAA,oBAAoBA,CAACpD,IAAI,EAAElH,KAAK,EAAE;QAChC,IAAI,CAACjC,UAAC,CAACyF,SAAS,CAAC0D,IAAI,CAACpJ,IAAI,CAACkF,IAAI,CAAC,EAAE,OAAA;AAClCsF,QAAAA,2BAA2B,CACzBpB,IAAI,EACJlI,IAAI,IAAIgB,KAAK,CAACgB,SAAS,CAAChC,IAAI,CAAC,EAC7B6B,mBAAmB,EACnBD,eAAe,EACfE,mBAAmB,EACnBC,WACF,CAAC,CAAA;OACF;AAEDwJ,MAAAA,mBAAmBA,CAACrD,IAAI,EAAElH,KAAK,EAAE;QAC/B,MAAM;UAAElC,IAAI;AAAEmK,UAAAA,MAAAA;AAAO,SAAC,GAAGf,IAAI,CAAA;AAC7B,QAAA,IAAInJ,UAAC,CAACyM,eAAe,CAACvC,MAAM,CAAC,EAAE,OAAA;AAC/B,QAAA,IAAI,CAACA,MAAM,IAAI,CAACf,IAAI,CAACuD,SAAS,EAAE,OAAA;AAChC,QAAA,IAAI,CAAC7B,6BAA6B,CAAC9K,IAAI,CAAC,EAAE,OAAA;AAC1CmJ,QAAAA,0BAA0B,CACxBC,IAAI,EACJlI,IAAI,IAAIgB,KAAK,CAACgB,SAAS,CAAChC,IAAI,CAAC,EAC7B6B,mBAAmB,EACnBD,eAAe,EACfE,mBAAmB,EACnBC,WACF,CAAC,CAAA;AACH,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;;;"}