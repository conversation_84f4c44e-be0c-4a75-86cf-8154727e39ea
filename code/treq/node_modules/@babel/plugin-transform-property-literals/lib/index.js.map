{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "ObjectProperty", "exit", "node", "key", "computed", "t", "isIdentifier", "isValidES3Identifier", "stringLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-property-literals\",\n\n    visitor: {\n      ObjectProperty: {\n        exit({ node }) {\n          const key = node.key;\n          if (\n            !node.computed &&\n            t.isIdentifier(key) &&\n            !t.isValidES3Identifier(key.name)\n          ) {\n            // default: \"bar\" -> \"default\": \"bar\"\n            node.key = t.stringLiteral(key.name);\n          }\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,6BAA6B;IAEnCC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAIA,CAAC;UAAEC;QAAK,CAAC,EAAE;UACb,MAAMC,GAAG,GAAGD,IAAI,CAACC,GAAG;UACpB,IACE,CAACD,IAAI,CAACE,QAAQ,IACdC,WAAC,CAACC,YAAY,CAACH,GAAG,CAAC,IACnB,CAACE,WAAC,CAACE,oBAAoB,CAACJ,GAAG,CAACL,IAAI,CAAC,EACjC;YAEAI,IAAI,CAACC,GAAG,GAAGE,WAAC,CAACG,aAAa,CAACL,GAAG,CAACL,IAAI,CAAC;UACtC;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}