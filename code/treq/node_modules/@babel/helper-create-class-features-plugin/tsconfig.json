/* This file is automatically generated by scripts/generators/tsconfig.js */
{
  "extends": [
    "../../tsconfig.base.json",
    "../../tsconfig.paths.json"
  ],
  "include": [
    "../../packages/babel-helper-create-class-features-plugin/src/**/*.ts",
    "../../lib/globals.d.ts",
    "../../scripts/repo-utils/*.d.ts"
  ],
  "references": [
    {
      "path": "../../packages/babel-helper-plugin-utils"
    },
    {
      "path": "../../packages/babel-helper-annotate-as-pure"
    },
    {
      "path": "../../packages/babel-helper-member-expression-to-functions"
    },
    {
      "path": "../../packages/babel-helper-optimise-call-expression"
    },
    {
      "path": "../../packages/babel-helper-replace-supers"
    },
    {
      "path": "../../packages/babel-helper-skip-transparent-expression-wrappers"
    }
  ]
}