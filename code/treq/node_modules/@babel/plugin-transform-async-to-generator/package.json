{"name": "@babel/plugin-transform-async-to-generator", "version": "7.24.7", "description": "Turn async functions into ES2015 generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-async-to-generator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-to-generator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-module-imports": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-remap-async-to-generator": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}