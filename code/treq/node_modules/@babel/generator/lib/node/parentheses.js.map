{"version": 3, "names": ["_t", "require", "isArrayTypeAnnotation", "isArrowFunctionExpression", "isBinaryExpression", "isCallExpression", "isExportDeclaration", "isForOfStatement", "isIndexedAccessType", "isMemberExpression", "isObjectPattern", "isOptionalMemberExpression", "isYieldExpression", "PRECEDENCE", "Map", "isTSTypeExpression", "nodeType", "isClassExtendsClause", "node", "parent", "parentType", "type", "superClass", "hasPostfixPart", "object", "callee", "tag", "NullableTypeAnnotation", "FunctionTypeAnnotation", "printStack", "length", "UpdateExpression", "ObjectExpression", "isFirstInContext", "DoExpression", "async", "Binary", "operator", "left", "parentPos", "get", "nodePos", "right", "undefined", "UnionTypeAnnotation", "OptionalIndexedAccessType", "objectType", "TSAsExpression", "TSUnionType", "TSInferType", "TSInstantiationExpression", "typeParameters", "BinaryExpression", "SequenceExpression", "test", "discriminant", "expression", "YieldExpression", "ClassExpression", "UnaryLike", "FunctionExpression", "ArrowFunctionExpression", "ConditionalExpression", "OptionalMemberExpression", "AssignmentExpression", "LogicalExpression", "Identifier", "_node$extra", "extra", "parenthesized", "rightType", "id", "name", "isFollowedByBracket", "computed", "optional", "checkParam", "expressionStatement", "arrowBody", "exportDefault", "forHead", "forInHead", "forOfHead", "i", "declaration", "body", "init", "expressions", "prefix"], "sources": ["../../src/node/parentheses.ts"], "sourcesContent": ["import {\n  isArrayTypeAnnotation,\n  isArrowFunctionExpression,\n  isBinaryExpression,\n  isCallExpression,\n  isExportDeclaration,\n  isForOfStatement,\n  isIndexedAccessType,\n  isMemberExpression,\n  isObjectPattern,\n  isOptionalMemberExpression,\n  isYieldExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nconst PRECEDENCE = new Map([\n  [\"||\", 0],\n  [\"??\", 0],\n  [\"|>\", 0],\n  [\"&&\", 1],\n  [\"|\", 2],\n  [\"^\", 3],\n  [\"&\", 4],\n  [\"==\", 5],\n  [\"===\", 5],\n  [\"!=\", 5],\n  [\"!==\", 5],\n  [\"<\", 6],\n  [\">\", 6],\n  [\"<=\", 6],\n  [\">=\", 6],\n  [\"in\", 6],\n  [\"instanceof\", 6],\n  [\">>\", 7],\n  [\"<<\", 7],\n  [\">>>\", 7],\n  [\"+\", 8],\n  [\"-\", 8],\n  [\"*\", 9],\n  [\"/\", 9],\n  [\"%\", 9],\n  [\"**\", 10],\n]);\n\nconst enum CheckParam {\n  expressionStatement = 1 << 0,\n  arrowBody = 1 << 1,\n  exportDefault = 1 << 2,\n  forHead = 1 << 3,\n  forInHead = 1 << 4,\n  forOfHead = 1 << 5,\n}\n\nfunction isTSTypeExpression(nodeType: string) {\n  return (\n    nodeType === \"TSAsExpression\" ||\n    nodeType === \"TSSatisfiesExpression\" ||\n    nodeType === \"TSTypeAssertion\"\n  );\n}\n\nconst isClassExtendsClause = (\n  node: t.Node,\n  parent: t.Node,\n): parent is t.Class => {\n  const parentType = parent.type;\n  return (\n    (parentType === \"ClassDeclaration\" || parentType === \"ClassExpression\") &&\n    parent.superClass === node\n  );\n};\n\nconst hasPostfixPart = (node: t.Node, parent: t.Node) => {\n  const parentType = parent.type;\n  return (\n    ((parentType === \"MemberExpression\" ||\n      parentType === \"OptionalMemberExpression\") &&\n      parent.object === node) ||\n    ((parentType === \"CallExpression\" ||\n      parentType === \"OptionalCallExpression\" ||\n      parentType === \"NewExpression\") &&\n      parent.callee === node) ||\n    (parentType === \"TaggedTemplateExpression\" && parent.tag === node) ||\n    parentType === \"TSNonNullExpression\"\n  );\n};\n\nexport function NullableTypeAnnotation(\n  node: t.NullableTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return isArrayTypeAnnotation(parent);\n}\n\nexport function FunctionTypeAnnotation(\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  if (printStack.length < 3) return;\n\n  const parentType = parent.type;\n  return (\n    // (() => A) | (() => B)\n    parentType === \"UnionTypeAnnotation\" ||\n    // (() => A) & (() => B)\n    parentType === \"IntersectionTypeAnnotation\" ||\n    // (() => A)[]\n    parentType === \"ArrayTypeAnnotation\" ||\n    // <T>(A: T): (T => T[]) => B => [A, B]\n    (parentType === \"TypeAnnotation\" &&\n      // Check grandparent\n      isArrowFunctionExpression(printStack[printStack.length - 3]))\n  );\n}\n\nexport function UpdateExpression(\n  node: t.UpdateExpression,\n  parent: t.Node,\n): boolean {\n  return hasPostfixPart(node, parent) || isClassExtendsClause(node, parent);\n}\n\nexport function ObjectExpression(\n  node: t.ObjectExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.arrowBody,\n  );\n}\n\nexport function DoExpression(\n  node: t.DoExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  // `async do` can start an expression statement\n  return (\n    !node.async && isFirstInContext(printStack, CheckParam.expressionStatement)\n  );\n}\n\nexport function Binary(\n  node: t.BinaryExpression,\n  parent: t.Node,\n): boolean | undefined {\n  const parentType = parent.type;\n  if (\n    node.operator === \"**\" &&\n    parentType === \"BinaryExpression\" &&\n    parent.operator === \"**\"\n  ) {\n    return parent.left === node;\n  }\n\n  if (isClassExtendsClause(node, parent)) {\n    return true;\n  }\n\n  if (\n    hasPostfixPart(node, parent) ||\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    parentType === \"AwaitExpression\"\n  ) {\n    return true;\n  }\n\n  if (parentType === \"BinaryExpression\" || parentType === \"LogicalExpression\") {\n    const parentPos = PRECEDENCE.get(parent.operator);\n    const nodePos = PRECEDENCE.get(node.operator);\n\n    if (\n      // Logical expressions with the same precedence don't need parens.\n      (parentPos === nodePos &&\n        parent.right === node &&\n        parentType !== \"LogicalExpression\") ||\n      parentPos > nodePos\n    ) {\n      return true;\n    }\n  }\n\n  return undefined;\n}\n\nexport function UnionTypeAnnotation(\n  node: t.UnionTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"ArrayTypeAnnotation\" ||\n    parentType === \"NullableTypeAnnotation\" ||\n    parentType === \"IntersectionTypeAnnotation\" ||\n    parentType === \"UnionTypeAnnotation\"\n  );\n}\n\nexport { UnionTypeAnnotation as IntersectionTypeAnnotation };\n\nexport function OptionalIndexedAccessType(\n  node: t.OptionalIndexedAccessType,\n  parent: t.Node,\n): boolean {\n  return isIndexedAccessType(parent) && parent.objectType === node;\n}\n\nexport function TSAsExpression() {\n  return true;\n}\n\nexport {\n  TSAsExpression as TSSatisfiesExpression,\n  TSAsExpression as TSTypeAssertion,\n};\n\nexport function TSUnionType(node: t.TSUnionType, parent: t.Node): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"TSArrayType\" ||\n    parentType === \"TSOptionalType\" ||\n    parentType === \"TSIntersectionType\" ||\n    parentType === \"TSUnionType\" ||\n    parentType === \"TSRestType\"\n  );\n}\n\nexport { TSUnionType as TSIntersectionType };\n\nexport function TSInferType(node: t.TSInferType, parent: t.Node): boolean {\n  const parentType = parent.type;\n  return parentType === \"TSArrayType\" || parentType === \"TSOptionalType\";\n}\n\nexport function TSInstantiationExpression(\n  node: t.TSInstantiationExpression,\n  parent: t.Node,\n) {\n  const parentType = parent.type;\n  return (\n    (parentType === \"CallExpression\" ||\n      parentType === \"OptionalCallExpression\" ||\n      parentType === \"NewExpression\" ||\n      parentType === \"TSInstantiationExpression\") &&\n    !!parent.typeParameters\n  );\n}\n\nexport function BinaryExpression(\n  node: t.BinaryExpression,\n  parent: t.Node,\n): boolean {\n  // let i = (1 in []);\n  // for ((1 in []);;);\n  if (node.operator === \"in\") {\n    const parentType = parent.type;\n    return (\n      parentType === \"VariableDeclarator\" ||\n      parentType === \"ForStatement\" ||\n      parentType === \"ForInStatement\" ||\n      parentType === \"ForOfStatement\"\n    );\n  }\n  return false;\n}\n\nexport function SequenceExpression(\n  node: t.SequenceExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    // Although parentheses wouldn't hurt around sequence\n    // expressions in the head of for loops, traditional style\n    // dictates that e.g. i++, j++ should not be wrapped with\n    // parentheses.\n    parentType === \"ForStatement\" ||\n    parentType === \"ThrowStatement\" ||\n    parentType === \"ReturnStatement\" ||\n    (parentType === \"IfStatement\" && parent.test === node) ||\n    (parentType === \"WhileStatement\" && parent.test === node) ||\n    (parentType === \"ForInStatement\" && parent.right === node) ||\n    (parentType === \"SwitchStatement\" && parent.discriminant === node) ||\n    (parentType === \"ExpressionStatement\" && parent.expression === node)\n  ) {\n    return false;\n  }\n\n  // Otherwise err on the side of overparenthesization, adding\n  // explicit exceptions above if this proves overzealous.\n  return true;\n}\n\nexport function YieldExpression(\n  node: t.YieldExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  return (\n    parentType === \"BinaryExpression\" ||\n    parentType === \"LogicalExpression\" ||\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    hasPostfixPart(node, parent) ||\n    (parentType === \"AwaitExpression\" && isYieldExpression(node)) ||\n    (parentType === \"ConditionalExpression\" && node === parent.test) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport { YieldExpression as AwaitExpression };\n\nexport function ClassExpression(\n  node: t.ClassExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function UnaryLike(\n  node:\n    | t.UnaryLike\n    | t.ArrowFunctionExpression\n    | t.ConditionalExpression\n    | t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    hasPostfixPart(node, parent) ||\n    (isBinaryExpression(parent) &&\n      parent.operator === \"**\" &&\n      parent.left === node) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport function FunctionExpression(\n  node: t.FunctionExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function ArrowFunctionExpression(\n  node: t.ArrowFunctionExpression,\n  parent: t.Node,\n): boolean {\n  return isExportDeclaration(parent) || ConditionalExpression(node, parent);\n}\n\nexport function ConditionalExpression(\n  node:\n    | t.ConditionalExpression\n    | t.ArrowFunctionExpression\n    | t.AssignmentExpression,\n  parent?: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (\n    parentType === \"UnaryExpression\" ||\n    parentType === \"SpreadElement\" ||\n    parentType === \"BinaryExpression\" ||\n    parentType === \"LogicalExpression\" ||\n    (parentType === \"ConditionalExpression\" && parent.test === node) ||\n    parentType === \"AwaitExpression\" ||\n    isTSTypeExpression(parentType)\n  ) {\n    return true;\n  }\n\n  return UnaryLike(node, parent);\n}\n\nexport function OptionalMemberExpression(\n  node: t.OptionalMemberExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    (isCallExpression(parent) && parent.callee === node) ||\n    (isMemberExpression(parent) && parent.object === node)\n  );\n}\n\nexport { OptionalMemberExpression as OptionalCallExpression };\n\nexport function AssignmentExpression(\n  node: t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  if (isObjectPattern(node.left)) {\n    return true;\n  } else {\n    return ConditionalExpression(node, parent);\n  }\n}\n\nexport function LogicalExpression(\n  node: t.LogicalExpression,\n  parent: t.Node,\n): boolean {\n  const parentType = parent.type;\n  if (isTSTypeExpression(parentType)) return true;\n  if (parentType !== \"LogicalExpression\") return false;\n  switch (node.operator) {\n    case \"||\":\n      return parent.operator === \"??\" || parent.operator === \"&&\";\n    case \"&&\":\n      return parent.operator === \"??\";\n    case \"??\":\n      return parent.operator !== \"??\";\n  }\n}\n\nexport function Identifier(\n  node: t.Identifier,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  const parentType = parent.type;\n  // 13.15.2 AssignmentExpression RS: Evaluation\n  // (fn) = function () {};\n  if (\n    node.extra?.parenthesized &&\n    parentType === \"AssignmentExpression\" &&\n    parent.left === node\n  ) {\n    const rightType = parent.right.type;\n    if (\n      (rightType === \"FunctionExpression\" || rightType === \"ClassExpression\") &&\n      parent.right.id == null\n    ) {\n      return true;\n    }\n  }\n  // Non-strict code allows the identifier `let`, but it cannot occur as-is in\n  // certain contexts to avoid ambiguity with contextual keyword `let`.\n  if (node.name === \"let\") {\n    // Some contexts only forbid `let [`, so check if the next token would\n    // be the left bracket of a computed member expression.\n    const isFollowedByBracket =\n      isMemberExpression(parent, {\n        object: node,\n        computed: true,\n      }) ||\n      isOptionalMemberExpression(parent, {\n        object: node,\n        computed: true,\n        optional: false,\n      });\n    return isFirstInContext(\n      printStack,\n      isFollowedByBracket\n        ? CheckParam.expressionStatement |\n            CheckParam.forHead |\n            CheckParam.forInHead |\n            CheckParam.forOfHead\n        : CheckParam.forOfHead,\n    );\n  }\n\n  // ECMAScript specifically forbids a for-of loop from starting with the\n  // token sequence `for (async of`, because it would be ambiguous with\n  // `for (async of => {};;)`, so we need to add extra parentheses.\n  //\n  // If the parent is a for-await-of loop (i.e. parent.await === true), the\n  // parentheses aren't strictly needed, but we add them anyway because\n  // some tools (including earlier Babel versions) can't parse\n  // `for await (async of [])` without them.\n  return (\n    node.name === \"async\" && isForOfStatement(parent) && node === parent.left\n  );\n}\n\n// Walk up the print stack to determine if our node can come first\n// in a particular context.\nfunction isFirstInContext(\n  printStack: Array<t.Node>,\n  checkParam: CheckParam,\n): boolean {\n  const expressionStatement = checkParam & CheckParam.expressionStatement;\n  const arrowBody = checkParam & CheckParam.arrowBody;\n  const exportDefault = checkParam & CheckParam.exportDefault;\n  const forHead = checkParam & CheckParam.forHead;\n  const forInHead = checkParam & CheckParam.forInHead;\n  const forOfHead = checkParam & CheckParam.forOfHead;\n\n  let i = printStack.length - 1;\n  if (i <= 0) return;\n  let node = printStack[i];\n  i--;\n  let parent = printStack[i];\n  while (i >= 0) {\n    const parentType = parent.type;\n    if (\n      (expressionStatement &&\n        parentType === \"ExpressionStatement\" &&\n        parent.expression === node) ||\n      (exportDefault &&\n        parentType === \"ExportDefaultDeclaration\" &&\n        node === parent.declaration) ||\n      (arrowBody &&\n        parentType === \"ArrowFunctionExpression\" &&\n        parent.body === node) ||\n      (forHead && parentType === \"ForStatement\" && parent.init === node) ||\n      (forInHead && parentType === \"ForInStatement\" && parent.left === node) ||\n      (forOfHead && parentType === \"ForOfStatement\" && parent.left === node)\n    ) {\n      return true;\n    }\n\n    if (\n      i > 0 &&\n      ((hasPostfixPart(node, parent) && parentType !== \"NewExpression\") ||\n        (parentType === \"SequenceExpression\" &&\n          parent.expressions[0] === node) ||\n        (parentType === \"UpdateExpression\" && !parent.prefix) ||\n        (parentType === \"ConditionalExpression\" && parent.test === node) ||\n        ((parentType === \"BinaryExpression\" ||\n          parentType === \"LogicalExpression\") &&\n          parent.left === node) ||\n        (parentType === \"AssignmentExpression\" && parent.left === node))\n    ) {\n      node = parent;\n      i--;\n      parent = printStack[i];\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAYsB;EAXpBC,qBAAqB;EACrBC,yBAAyB;EACzBC,kBAAkB;EAClBC,gBAAgB;EAChBC,mBAAmB;EACnBC,gBAAgB;EAChBC,mBAAmB;EACnBC,kBAAkB;EAClBC,eAAe;EACfC,0BAA0B;EAC1BC;AAAiB,IAAAZ,EAAA;AAGnB,MAAMa,UAAU,GAAG,IAAIC,GAAG,CAAC,CACzB,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,YAAY,EAAE,CAAC,CAAC,EACjB,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,KAAK,EAAE,CAAC,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,IAAI,EAAE,EAAE,CAAC,CACX,CAAC;AAWF,SAASC,kBAAkBA,CAACC,QAAgB,EAAE;EAC5C,OACEA,QAAQ,KAAK,gBAAgB,IAC7BA,QAAQ,KAAK,uBAAuB,IACpCA,QAAQ,KAAK,iBAAiB;AAElC;AAEA,MAAMC,oBAAoB,GAAGA,CAC3BC,IAAY,EACZC,MAAc,KACQ;EACtB,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACE,CAACD,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,iBAAiB,KACtED,MAAM,CAACG,UAAU,KAAKJ,IAAI;AAE9B,CAAC;AAED,MAAMK,cAAc,GAAGA,CAACL,IAAY,EAAEC,MAAc,KAAK;EACvD,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACG,CAACD,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,0BAA0B,KACzCD,MAAM,CAACK,MAAM,KAAKN,IAAI,IACvB,CAACE,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,eAAe,KAC9BD,MAAM,CAACM,MAAM,KAAKP,IAAK,IACxBE,UAAU,KAAK,0BAA0B,IAAID,MAAM,CAACO,GAAG,KAAKR,IAAK,IAClEE,UAAU,KAAK,qBAAqB;AAExC,CAAC;AAEM,SAASO,sBAAsBA,CACpCT,IAA8B,EAC9BC,MAAc,EACL;EACT,OAAOjB,qBAAqB,CAACiB,MAAM,CAAC;AACtC;AAEO,SAASS,sBAAsBA,CACpCV,IAA8B,EAC9BC,MAAc,EACdU,UAAyB,EAChB;EACT,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;EAE3B,MAAMV,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OAEED,UAAU,KAAK,qBAAqB,IAEpCA,UAAU,KAAK,4BAA4B,IAE3CA,UAAU,KAAK,qBAAqB,IAEnCA,UAAU,KAAK,gBAAgB,IAE9BjB,yBAAyB,CAAC0B,UAAU,CAACA,UAAU,CAACC,MAAM,GAAG,CAAC,CAAC,CAAE;AAEnE;AAEO,SAASC,gBAAgBA,CAC9Bb,IAAwB,EACxBC,MAAc,EACL;EACT,OAAOI,cAAc,CAACL,IAAI,EAAEC,MAAM,CAAC,IAAIF,oBAAoB,CAACC,IAAI,EAAEC,MAAM,CAAC;AAC3E;AAEO,SAASa,gBAAgBA,CAC9Bd,IAAwB,EACxBC,MAAc,EACdU,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KACF,CAAC;AACH;AAEO,SAASK,YAAYA,CAC1BhB,IAAoB,EACpBC,MAAc,EACdU,UAAyB,EAChB;EAET,OACE,CAACX,IAAI,CAACiB,KAAK,IAAIF,gBAAgB,CAACJ,UAAU,GAAgC,CAAC;AAE/E;AAEO,SAASO,MAAMA,CACpBlB,IAAwB,EACxBC,MAAc,EACO;EACrB,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACEH,IAAI,CAACmB,QAAQ,KAAK,IAAI,IACtBjB,UAAU,KAAK,kBAAkB,IACjCD,MAAM,CAACkB,QAAQ,KAAK,IAAI,EACxB;IACA,OAAOlB,MAAM,CAACmB,IAAI,KAAKpB,IAAI;EAC7B;EAEA,IAAID,oBAAoB,CAACC,IAAI,EAAEC,MAAM,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;EAEA,IACEI,cAAc,CAACL,IAAI,EAAEC,MAAM,CAAC,IAC5BC,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,iBAAiB,EAChC;IACA,OAAO,IAAI;EACb;EAEA,IAAIA,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,mBAAmB,EAAE;IAC3E,MAAMmB,SAAS,GAAG1B,UAAU,CAAC2B,GAAG,CAACrB,MAAM,CAACkB,QAAQ,CAAC;IACjD,MAAMI,OAAO,GAAG5B,UAAU,CAAC2B,GAAG,CAACtB,IAAI,CAACmB,QAAQ,CAAC;IAE7C,IAEGE,SAAS,KAAKE,OAAO,IACpBtB,MAAM,CAACuB,KAAK,KAAKxB,IAAI,IACrBE,UAAU,KAAK,mBAAmB,IACpCmB,SAAS,GAAGE,OAAO,EACnB;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAOE,SAAS;AAClB;AAEO,SAASC,mBAAmBA,CACjC1B,IAA2B,EAC3BC,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,qBAAqB,IACpCA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,4BAA4B,IAC3CA,UAAU,KAAK,qBAAqB;AAExC;AAIO,SAASyB,yBAAyBA,CACvC3B,IAAiC,EACjCC,MAAc,EACL;EACT,OAAOX,mBAAmB,CAACW,MAAM,CAAC,IAAIA,MAAM,CAAC2B,UAAU,KAAK5B,IAAI;AAClE;AAEO,SAAS6B,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAI;AACb;AAOO,SAASC,WAAWA,CAAC9B,IAAmB,EAAEC,MAAc,EAAW;EACxE,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,aAAa,IAC5BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,oBAAoB,IACnCA,UAAU,KAAK,aAAa,IAC5BA,UAAU,KAAK,YAAY;AAE/B;AAIO,SAAS6B,WAAWA,CAAC/B,IAAmB,EAAEC,MAAc,EAAW;EACxE,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OAAOD,UAAU,KAAK,aAAa,IAAIA,UAAU,KAAK,gBAAgB;AACxE;AAEO,SAAS8B,yBAAyBA,CACvChC,IAAiC,EACjCC,MAAc,EACd;EACA,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACE,CAACD,UAAU,KAAK,gBAAgB,IAC9BA,UAAU,KAAK,wBAAwB,IACvCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,2BAA2B,KAC5C,CAAC,CAACD,MAAM,CAACgC,cAAc;AAE3B;AAEO,SAASC,gBAAgBA,CAC9BlC,IAAwB,EACxBC,MAAc,EACL;EAGT,IAAID,IAAI,CAACmB,QAAQ,KAAK,IAAI,EAAE;IAC1B,MAAMjB,UAAU,GAAGD,MAAM,CAACE,IAAI;IAC9B,OACED,UAAU,KAAK,oBAAoB,IACnCA,UAAU,KAAK,cAAc,IAC7BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,gBAAgB;EAEnC;EACA,OAAO,KAAK;AACd;AAEO,SAASiC,kBAAkBA,CAChCnC,IAA0B,EAC1BC,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IAKED,UAAU,KAAK,cAAc,IAC7BA,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,iBAAiB,IAC/BA,UAAU,KAAK,aAAa,IAAID,MAAM,CAACmC,IAAI,KAAKpC,IAAK,IACrDE,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACmC,IAAI,KAAKpC,IAAK,IACxDE,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACuB,KAAK,KAAKxB,IAAK,IACzDE,UAAU,KAAK,iBAAiB,IAAID,MAAM,CAACoC,YAAY,KAAKrC,IAAK,IACjEE,UAAU,KAAK,qBAAqB,IAAID,MAAM,CAACqC,UAAU,KAAKtC,IAAK,EACpE;IACA,OAAO,KAAK;EACd;EAIA,OAAO,IAAI;AACb;AAEO,SAASuC,eAAeA,CAC7BvC,IAAuB,EACvBC,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,OACED,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,IAClCA,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BG,cAAc,CAACL,IAAI,EAAEC,MAAM,CAAC,IAC3BC,UAAU,KAAK,iBAAiB,IAAIR,iBAAiB,CAACM,IAAI,CAAE,IAC5DE,UAAU,KAAK,uBAAuB,IAAIF,IAAI,KAAKC,MAAM,CAACmC,IAAK,IAChErC,oBAAoB,CAACC,IAAI,EAAEC,MAAM,CAAC;AAEtC;AAIO,SAASuC,eAAeA,CAC7BxC,IAAuB,EACvBC,MAAc,EACdU,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KACF,CAAC;AACH;AAEO,SAAS8B,SAASA,CACvBzC,IAI0B,EAC1BC,MAAc,EACL;EACT,OACEI,cAAc,CAACL,IAAI,EAAEC,MAAM,CAAC,IAC3Bf,kBAAkB,CAACe,MAAM,CAAC,IACzBA,MAAM,CAACkB,QAAQ,KAAK,IAAI,IACxBlB,MAAM,CAACmB,IAAI,KAAKpB,IAAK,IACvBD,oBAAoB,CAACC,IAAI,EAAEC,MAAM,CAAC;AAEtC;AAEO,SAASyC,kBAAkBA,CAChC1C,IAA0B,EAC1BC,MAAc,EACdU,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KACF,CAAC;AACH;AAEO,SAASgC,uBAAuBA,CACrC3C,IAA+B,EAC/BC,MAAc,EACL;EACT,OAAOb,mBAAmB,CAACa,MAAM,CAAC,IAAI2C,qBAAqB,CAAC5C,IAAI,EAAEC,MAAM,CAAC;AAC3E;AAEO,SAAS2C,qBAAqBA,CACnC5C,IAG0B,EAC1BC,MAAe,EACN;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IACED,UAAU,KAAK,iBAAiB,IAChCA,UAAU,KAAK,eAAe,IAC9BA,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,IACjCA,UAAU,KAAK,uBAAuB,IAAID,MAAM,CAACmC,IAAI,KAAKpC,IAAK,IAChEE,UAAU,KAAK,iBAAiB,IAChCL,kBAAkB,CAACK,UAAU,CAAC,EAC9B;IACA,OAAO,IAAI;EACb;EAEA,OAAOuC,SAAS,CAACzC,IAAI,EAAEC,MAAM,CAAC;AAChC;AAEO,SAAS4C,wBAAwBA,CACtC7C,IAAgC,EAChCC,MAAc,EACL;EACT,OACGd,gBAAgB,CAACc,MAAM,CAAC,IAAIA,MAAM,CAACM,MAAM,KAAKP,IAAI,IAClDT,kBAAkB,CAACU,MAAM,CAAC,IAAIA,MAAM,CAACK,MAAM,KAAKN,IAAK;AAE1D;AAIO,SAAS8C,oBAAoBA,CAClC9C,IAA4B,EAC5BC,MAAc,EACL;EACT,IAAIT,eAAe,CAACQ,IAAI,CAACoB,IAAI,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOwB,qBAAqB,CAAC5C,IAAI,EAAEC,MAAM,CAAC;EAC5C;AACF;AAEO,SAAS8C,iBAAiBA,CAC/B/C,IAAyB,EACzBC,MAAc,EACL;EACT,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI;EAC9B,IAAIN,kBAAkB,CAACK,UAAU,CAAC,EAAE,OAAO,IAAI;EAC/C,IAAIA,UAAU,KAAK,mBAAmB,EAAE,OAAO,KAAK;EACpD,QAAQF,IAAI,CAACmB,QAAQ;IACnB,KAAK,IAAI;MACP,OAAOlB,MAAM,CAACkB,QAAQ,KAAK,IAAI,IAAIlB,MAAM,CAACkB,QAAQ,KAAK,IAAI;IAC7D,KAAK,IAAI;MACP,OAAOlB,MAAM,CAACkB,QAAQ,KAAK,IAAI;IACjC,KAAK,IAAI;MACP,OAAOlB,MAAM,CAACkB,QAAQ,KAAK,IAAI;EACnC;AACF;AAEO,SAAS6B,UAAUA,CACxBhD,IAAkB,EAClBC,MAAc,EACdU,UAAyB,EAChB;EAAA,IAAAsC,WAAA;EACT,MAAM/C,UAAU,GAAGD,MAAM,CAACE,IAAI;EAG9B,IACE,CAAA8C,WAAA,GAAAjD,IAAI,CAACkD,KAAK,aAAVD,WAAA,CAAYE,aAAa,IACzBjD,UAAU,KAAK,sBAAsB,IACrCD,MAAM,CAACmB,IAAI,KAAKpB,IAAI,EACpB;IACA,MAAMoD,SAAS,GAAGnD,MAAM,CAACuB,KAAK,CAACrB,IAAI;IACnC,IACE,CAACiD,SAAS,KAAK,oBAAoB,IAAIA,SAAS,KAAK,iBAAiB,KACtEnD,MAAM,CAACuB,KAAK,CAAC6B,EAAE,IAAI,IAAI,EACvB;MACA,OAAO,IAAI;IACb;EACF;EAGA,IAAIrD,IAAI,CAACsD,IAAI,KAAK,KAAK,EAAE;IAGvB,MAAMC,mBAAmB,GACvBhE,kBAAkB,CAACU,MAAM,EAAE;MACzBK,MAAM,EAAEN,IAAI;MACZwD,QAAQ,EAAE;IACZ,CAAC,CAAC,IACF/D,0BAA0B,CAACQ,MAAM,EAAE;MACjCK,MAAM,EAAEN,IAAI;MACZwD,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACJ,OAAO1C,gBAAgB,CACrBJ,UAAU,EACV4C,mBAAmB,GACf,KACoB,KACE,KACA,KAE5B,CAAC;EACH;EAUA,OACEvD,IAAI,CAACsD,IAAI,KAAK,OAAO,IAAIjE,gBAAgB,CAACY,MAAM,CAAC,IAAID,IAAI,KAAKC,MAAM,CAACmB,IAAI;AAE7E;AAIA,SAASL,gBAAgBA,CACvBJ,UAAyB,EACzB+C,UAAsB,EACb;EACT,MAAMC,mBAAmB,GAAGD,UAAU,IAAiC;EACvE,MAAME,SAAS,GAAGF,UAAU,IAAuB;EACnD,MAAMG,aAAa,GAAGH,UAAU,IAA2B;EAC3D,MAAMI,OAAO,GAAGJ,UAAU,IAAqB;EAC/C,MAAMK,SAAS,GAAGL,UAAU,KAAuB;EACnD,MAAMM,SAAS,GAAGN,UAAU,KAAuB;EAEnD,IAAIO,CAAC,GAAGtD,UAAU,CAACC,MAAM,GAAG,CAAC;EAC7B,IAAIqD,CAAC,IAAI,CAAC,EAAE;EACZ,IAAIjE,IAAI,GAAGW,UAAU,CAACsD,CAAC,CAAC;EACxBA,CAAC,EAAE;EACH,IAAIhE,MAAM,GAAGU,UAAU,CAACsD,CAAC,CAAC;EAC1B,OAAOA,CAAC,IAAI,CAAC,EAAE;IACb,MAAM/D,UAAU,GAAGD,MAAM,CAACE,IAAI;IAC9B,IACGwD,mBAAmB,IAClBzD,UAAU,KAAK,qBAAqB,IACpCD,MAAM,CAACqC,UAAU,KAAKtC,IAAI,IAC3B6D,aAAa,IACZ3D,UAAU,KAAK,0BAA0B,IACzCF,IAAI,KAAKC,MAAM,CAACiE,WAAY,IAC7BN,SAAS,IACR1D,UAAU,KAAK,yBAAyB,IACxCD,MAAM,CAACkE,IAAI,KAAKnE,IAAK,IACtB8D,OAAO,IAAI5D,UAAU,KAAK,cAAc,IAAID,MAAM,CAACmE,IAAI,KAAKpE,IAAK,IACjE+D,SAAS,IAAI7D,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACmB,IAAI,KAAKpB,IAAK,IACrEgE,SAAS,IAAI9D,UAAU,KAAK,gBAAgB,IAAID,MAAM,CAACmB,IAAI,KAAKpB,IAAK,EACtE;MACA,OAAO,IAAI;IACb;IAEA,IACEiE,CAAC,GAAG,CAAC,KACH5D,cAAc,CAACL,IAAI,EAAEC,MAAM,CAAC,IAAIC,UAAU,KAAK,eAAe,IAC7DA,UAAU,KAAK,oBAAoB,IAClCD,MAAM,CAACoE,WAAW,CAAC,CAAC,CAAC,KAAKrE,IAAK,IAChCE,UAAU,KAAK,kBAAkB,IAAI,CAACD,MAAM,CAACqE,MAAO,IACpDpE,UAAU,KAAK,uBAAuB,IAAID,MAAM,CAACmC,IAAI,KAAKpC,IAAK,IAC/D,CAACE,UAAU,KAAK,kBAAkB,IACjCA,UAAU,KAAK,mBAAmB,KAClCD,MAAM,CAACmB,IAAI,KAAKpB,IAAK,IACtBE,UAAU,KAAK,sBAAsB,IAAID,MAAM,CAACmB,IAAI,KAAKpB,IAAK,CAAC,EAClE;MACAA,IAAI,GAAGC,MAAM;MACbgE,CAAC,EAAE;MACHhE,MAAM,GAAGU,UAAU,CAACsD,CAAC,CAAC;IACxB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}